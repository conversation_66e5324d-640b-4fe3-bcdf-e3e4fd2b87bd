import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/services/content_service.dart';
import 'subscription_activation_page.dart';
import 'subject_detail_page.dart';
import '../../../../shared/models/subject_model.dart';

class StudentHomePage extends StatefulWidget {
  const StudentHomePage({super.key});

  @override
  State<StudentHomePage> createState() => _StudentHomePageState();
}

class _StudentHomePageState extends State<StudentHomePage> {
  int _currentIndex = 1; // البدء من صفحة المواد بدلاً من تفعيل الاشتراك

  final List<Widget> _pages = [
    const SubscriptionActivationPage(), // تفعيل الاشتراك
    const SubjectsPage(), // المواد
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Smart Test - الطالب',
          style: Theme.of(
            context,
          ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
        ),
        foregroundColor: Colors.white,
      ),
      body: _pages[_currentIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.white,
          selectedItemColor: AppTheme.primaryColor,
          unselectedItemColor: AppTheme.textSecondaryColor,
          selectedLabelStyle: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.normal,
          ),
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.card_membership_outlined),
              activeIcon: Icon(Icons.card_membership),
              label: 'تفعيل الاشتراك',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.book_outlined),
              activeIcon: Icon(Icons.book),
              label: 'المواد',
            ),
          ],
        ),
      ),
    );
  }
}

class SubjectsPage extends StatefulWidget {
  const SubjectsPage({super.key});

  @override
  State<SubjectsPage> createState() => _SubjectsPageState();
}

class _SubjectsPageState extends State<SubjectsPage> {
  List<Subject> _subjects = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSubjects();
  }

  Future<void> _loadSubjects() async {
    try {
      final subjects = await ContentService.instance.getAllSubjects();
      setState(() {
        _subjects = subjects;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading subjects: $e');
      // في حالة فشل تحميل البيانات من Firebase، استخدم بيانات تجريبية
      final mockSubjects = [
        Subject(
          id: 'math',
          name: 'الرياضيات',
          description: 'مادة الرياضيات للثانوية العامة',
          iconUrl: '',
          color: '#6C5CE7',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Subject(
          id: 'physics',
          name: 'الفيزياء',
          description: 'مادة الفيزياء للثانوية العامة',
          iconUrl: '',
          color: '#00B894',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Subject(
          id: 'chemistry',
          name: 'الكيمياء',
          description: 'مادة الكيمياء للثانوية العامة',
          iconUrl: '',
          color: '#E17055',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      setState(() {
        _subjects = mockSubjects;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_subjects.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.book_outlined,
              size: 80,
              color: AppTheme.textSecondaryColor,
            ),
            SizedBox(height: 16.h),
            Text(
              'لا توجد مواد متاحة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'سيتم إضافة المواد قريباً',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }
    return RefreshIndicator(
      onRefresh: _loadSubjects,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المواد الدراسية',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _isLoading = true;
                    });
                    _loadSubjects();
                  },
                  icon: Icon(Icons.refresh, color: AppTheme.primaryColor),
                  tooltip: 'تحديث',
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Expanded(
              child: GridView.builder(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16.w,
                  mainAxisSpacing: 16.h,
                  childAspectRatio: 0.8,
                ),
                itemCount: _subjects.length,
                itemBuilder: (context, index) {
                  return _buildSubjectCard(context, _subjects[index]);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubjectCard(BuildContext context, Subject subject) {
    // تحويل اللون من hex string إلى Color
    Color subjectColor = AppTheme.primaryColor;
    try {
      if (subject.color.isNotEmpty) {
        final colorString = subject.color.replaceAll('#', '');
        subjectColor = Color(int.parse('FF$colorString', radix: 16));
      }
    } catch (e) {
      // استخدام اللون الافتراضي في حالة الخطأ
    }

    // اختيار أيقونة بناءً على اسم المادة
    IconData subjectIcon = Icons.book;
    if (subject.name.contains('رياضيات')) {
      subjectIcon = Icons.calculate;
    } else if (subject.name.contains('فيزياء')) {
      subjectIcon = Icons.science;
    } else if (subject.name.contains('كيمياء')) {
      subjectIcon = Icons.biotech;
    } else if (subject.name.contains('أحياء')) {
      subjectIcon = Icons.eco;
    } else if (subject.name.contains('عربية')) {
      subjectIcon = Icons.language;
    } else if (subject.name.contains('إنجليزية')) {
      subjectIcon = Icons.translate;
    }

    return Card(
      elevation: 4,
      shadowColor: subjectColor.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SubjectDetailPage(subject: subject),
            ),
          );
        },
        borderRadius: BorderRadius.circular(16.r),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              colors: [
                subjectColor.withValues(alpha: 0.1),
                subjectColor.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 60.w,
                  height: 60.h,
                  decoration: BoxDecoration(
                    color: subjectColor,
                    borderRadius: BorderRadius.circular(30.r),
                  ),
                  child: Icon(subjectIcon, color: Colors.white, size: 30.sp),
                ),
                SizedBox(height: 12.h),
                Text(
                  subject.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 8.h),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: subjectColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    subject.isActive ? 'متاح' : 'غير متاح',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: subjectColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
