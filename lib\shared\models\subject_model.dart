import 'package:flutter/foundation.dart';

class Subject {
  final String id;
  final String name;
  final String description;
  final String iconUrl;
  final String color;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Subject({
    required this.id,
    required this.name,
    required this.description,
    this.iconUrl = '',
    this.color = '#6C5CE7',
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Subject.fromMap(Map<String, dynamic> map) {
    return Subject(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      iconUrl: map['iconUrl'] ?? '',
      color: map['color'] ?? '#6C5CE7',
      isActive: map['isActive'] ?? true,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt'] ?? 0),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'color': color,
      'isActive': isActive,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  Subject copyWith({
    String? id,
    String? name,
    String? description,
    String? iconUrl,
    String? color,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Subject(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // دوال Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'color': color,
      'isActive': isActive,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  factory Subject.fromFirestore(Map<String, dynamic> data, String documentId) {
    return Subject(
      id: documentId,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      iconUrl: data['iconUrl'] ?? '',
      color: data['color'] ?? '#6C5CE7',
      isActive: data['isActive'] ?? true,
      createdAt: _parseDateTime(data['createdAt']),
      updatedAt: _parseDateTime(data['updatedAt']),
    );
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();

    try {
      // إذا كان Timestamp من Firestore
      if (value.runtimeType.toString().contains('Timestamp')) {
        return value.toDate();
      }

      // إذا كان int (milliseconds)
      if (value is int) {
        return DateTime.fromMillisecondsSinceEpoch(value);
      }

      // إذا كان String
      if (value is String) {
        return DateTime.tryParse(value) ?? DateTime.now();
      }

      // إذا كان DateTime بالفعل
      if (value is DateTime) {
        return value;
      }
    } catch (e) {
      debugPrint('خطأ في تحويل التاريخ: $e');
    }

    return DateTime.now();
  }
}
