import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/unit_model.dart';

class ContentService extends ChangeNotifier {
  static final ContentService _instance = ContentService._internal();
  static ContentService get instance => _instance;
  ContentService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// الحصول على وحدات المادة
  Future<List<Unit>> getSubjectUnits(String subjectId) async {
    try {
      final querySnapshot = await _firestore
          .collection('units')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return querySnapshot.docs
          .map((doc) => Unit.fromMap(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل الوحدات: $e');
      return [];
    }
  }

  /// الحصول على دروس الوحدة
  Future<List<Lesson>> getUnitLessons(String unitId) async {
    try {
      final querySnapshot = await _firestore
          .collection('lessons')
          .where('unitId', isEqualTo: unitId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return querySnapshot.docs
          .map((doc) => Lesson.fromMap(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل الدروس: $e');
      return [];
    }
  }

  /// إضافة وحدة جديدة
  Future<void> addUnit(Unit unit) async {
    try {
      await _firestore
          .collection('units')
          .doc(unit.id)
          .set(unit.toMap());
      
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في إضافة الوحدة: $e');
      rethrow;
    }
  }

  /// تحديث وحدة
  Future<void> updateUnit(Unit unit) async {
    try {
      await _firestore
          .collection('units')
          .doc(unit.id)
          .update(unit.toMap());
      
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث الوحدة: $e');
      rethrow;
    }
  }

  /// حذف وحدة
  Future<void> deleteUnit(String unitId) async {
    try {
      await _firestore
          .collection('units')
          .doc(unitId)
          .delete();
      
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف الوحدة: $e');
      rethrow;
    }
  }

  /// إضافة درس جديد
  Future<void> addLesson(Lesson lesson) async {
    try {
      await _firestore
          .collection('lessons')
          .doc(lesson.id)
          .set(lesson.toMap());
      
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في إضافة الدرس: $e');
      rethrow;
    }
  }

  /// تحديث درس
  Future<void> updateLesson(Lesson lesson) async {
    try {
      await _firestore
          .collection('lessons')
          .doc(lesson.id)
          .update(lesson.toMap());
      
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث الدرس: $e');
      rethrow;
    }
  }

  /// حذف درس
  Future<void> deleteLesson(String lessonId) async {
    try {
      await _firestore
          .collection('lessons')
          .doc(lessonId)
          .delete();
      
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف الدرس: $e');
      rethrow;
    }
  }

  /// البحث في الوحدات
  Future<List<Unit>> searchUnits(String query, String subjectId) async {
    try {
      final querySnapshot = await _firestore
          .collection('units')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .get();

      final units = querySnapshot.docs
          .map((doc) => Unit.fromMap(doc.data()))
          .toList();

      // فلترة النتائج محلياً
      return units.where((unit) {
        return unit.name.toLowerCase().contains(query.toLowerCase()) ||
               unit.description.toLowerCase().contains(query.toLowerCase());
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الوحدات: $e');
      return [];
    }
  }

  /// البحث في الدروس
  Future<List<Lesson>> searchLessons(String query, String unitId) async {
    try {
      final querySnapshot = await _firestore
          .collection('lessons')
          .where('unitId', isEqualTo: unitId)
          .where('isActive', isEqualTo: true)
          .get();

      final lessons = querySnapshot.docs
          .map((doc) => Lesson.fromMap(doc.data()))
          .toList();

      // فلترة النتائج محلياً
      return lessons.where((lesson) {
        return lesson.name.toLowerCase().contains(query.toLowerCase()) ||
               lesson.description.toLowerCase().contains(query.toLowerCase());
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الدروس: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات المحتوى
  Future<Map<String, int>> getContentStats(String subjectId) async {
    try {
      final unitsSnapshot = await _firestore
          .collection('units')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .get();

      int totalLessons = 0;
      for (final unitDoc in unitsSnapshot.docs) {
        final lessonsSnapshot = await _firestore
            .collection('lessons')
            .where('unitId', isEqualTo: unitDoc.id)
            .where('isActive', isEqualTo: true)
            .get();
        totalLessons += lessonsSnapshot.docs.length;
      }

      return {
        'units': unitsSnapshot.docs.length,
        'lessons': totalLessons,
      };
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات المحتوى: $e');
      return {'units': 0, 'lessons': 0};
    }
  }

  /// تفعيل/إلغاء تفعيل وحدة
  Future<void> toggleUnitStatus(String unitId, bool isActive) async {
    try {
      await _firestore
          .collection('units')
          .doc(unitId)
          .update({'isActive': isActive, 'updatedAt': Timestamp.now()});
      
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تغيير حالة الوحدة: $e');
      rethrow;
    }
  }

  /// تفعيل/إلغاء تفعيل درس
  Future<void> toggleLessonStatus(String lessonId, bool isActive) async {
    try {
      await _firestore
          .collection('lessons')
          .doc(lessonId)
          .update({'isActive': isActive, 'updatedAt': Timestamp.now()});
      
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تغيير حالة الدرس: $e');
      rethrow;
    }
  }
}
