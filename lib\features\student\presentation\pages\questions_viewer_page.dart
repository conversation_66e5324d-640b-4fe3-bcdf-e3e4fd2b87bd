import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/question_model.dart';
import '../../../../shared/services/exam_service.dart';
import '../../../../shared/services/student_data_service.dart';
import 'subject_units_page.dart';

class QuestionsViewerPage extends StatefulWidget {
  final String title;
  final Subject subject;
  final String? unitId;
  final String? lessonId;
  final QuestionFilterType questionType;

  const QuestionsViewerPage({
    super.key,
    required this.title,
    required this.subject,
    this.unitId,
    this.lessonId,
    required this.questionType,
  });

  @override
  State<QuestionsViewerPage> createState() => _QuestionsViewerPageState();
}

class _QuestionsViewerPageState extends State<QuestionsViewerPage> {
  List<Question> _questions = [];
  Map<String, List<String>> _userAnswers = {};
  Map<String, bool> _questionResults = {};
  Map<String, bool> _favoriteQuestions = {};
  Map<String, String> _questionNotes = {};
  bool _isLoading = true;
  bool _showResults = false;

  // إحصائيات
  int get _totalQuestions => _questions.length;
  int get _correctAnswers =>
      _questionResults.values.where((result) => result).length;
  int get _wrongAnswers =>
      _questionResults.values.where((result) => !result).length;

  @override
  void initState() {
    super.initState();
    _loadQuestions();
    _loadUserData();
  }

  /// تحميل بيانات المستخدم المحفوظة
  Future<void> _loadUserData() async {
    for (final question in _questions) {
      // تحميل حالة المفضلة
      final isFavorite = await StudentDataService.instance.isFavorite(
        question.id,
      );
      _favoriteQuestions[question.id] = isFavorite;

      // تحميل الملاحظات
      final note = await StudentDataService.instance.getQuestionNote(
        question.id,
      );
      if (note.isNotEmpty) {
        _questionNotes[question.id] = note;
      }

      // تحميل النتائج
      final result = await StudentDataService.instance.getQuestionResult(
        question.id,
      );
      if (result != null) {
        _questionResults[question.id] = result;
      }

      // تحميل الإجابات
      final answers = await StudentDataService.instance
          .getUserAnswersForQuestion(question.id);
      if (answers.isNotEmpty) {
        _userAnswers[question.id] = answers;
      }
    }

    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _loadQuestions() async {
    try {
      List<Question> questions = [];

      switch (widget.questionType) {
        case QuestionFilterType.unit:
          if (widget.unitId != null) {
            questions = await ExamService.instance.getQuestionsByUnit(
              widget.unitId!,
              false,
            );
          }
          break;
        case QuestionFilterType.lesson:
          if (widget.lessonId != null) {
            questions = await ExamService.instance.getQuestionsByLesson(
              widget.lessonId!,
              false,
            );
          }
          break;
        case QuestionFilterType.course:
          // تحميل أسئلة الدورات
          questions = await ExamService.instance.getQuestionsByUnit(
            widget.unitId ?? '',
            true,
          );
          break;
        case QuestionFilterType.favorite:
          // تحميل الأسئلة المفضلة - سنحتاج تطبيق هذا لاحقاً
          break;
        case QuestionFilterType.wrong:
          // تحميل الأسئلة الخاطئة - سنحتاج تطبيق هذا لاحقاً
          break;
      }

      setState(() {
        _questions = questions;
        _isLoading = false;
      });

      // تحميل بيانات المستخدم بعد تحميل الأسئلة
      await _loadUserData();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          widget.title,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
        ),
        foregroundColor: Colors.white,
        actions: [
          // إحصائيات سريعة
          _buildQuickStats(),
          SizedBox(width: 16.w),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _questions.isEmpty
          ? _buildEmptyState()
          : Column(
              children: [
                // شريط الإحصائيات
                _buildStatsBar(),

                // الأسئلة
                Expanded(
                  child: PageView.builder(
                    scrollDirection: Axis.vertical,
                    itemCount: _questions.length,
                    itemBuilder: (context, index) {
                      return _buildQuestionCard(_questions[index], index);
                    },
                  ),
                ),

                // شريط الأزرار السفلي
                _buildBottomActionBar(),
              ],
            ),
    );
  }

  Widget _buildQuickStats() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Text(
        '$_totalQuestions أسئلة',
        style: TextStyle(
          color: Colors.white,
          fontSize: 12.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildStatsBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // العدد الكلي
          _buildStatItem(
            icon: Icons.quiz_outlined,
            label: 'الكل',
            value: _totalQuestions.toString(),
            color: AppTheme.primaryColor,
          ),

          SizedBox(width: 16.w),

          // الصحيح
          _buildStatItem(
            icon: Icons.check_circle_outline,
            label: 'صحيح',
            value: _correctAnswers.toString(),
            color: AppTheme.successColor,
          ),

          SizedBox(width: 16.w),

          // الخاطئ
          _buildStatItem(
            icon: Icons.cancel_outlined,
            label: 'خاطئ',
            value: _wrongAnswers.toString(),
            color: AppTheme.errorColor,
          ),

          const Spacer(),

          // زر تصحيح الكل
          ElevatedButton.icon(
            onPressed: _checkAllAnswers,
            icon: Icon(Icons.fact_check, size: 16.sp),
            label: Text('تصحيح الكل'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16.sp),
        SizedBox(width: 4.w),
        Text(
          value,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        SizedBox(width: 2.w),
        Text(
          label,
          style: TextStyle(fontSize: 12.sp, color: AppTheme.textSecondaryColor),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.quiz_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد أسئلة متاحة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة الأسئلة قريباً',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionCard(Question question, int index) {
    final isAnswered = _userAnswers.containsKey(question.id);
    final isCorrect = _questionResults[question.id] ?? false;
    final isFavorite = _favoriteQuestions[question.id] ?? false;
    final hasNote = _questionNotes.containsKey(question.id);

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Card(
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Container(
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
            gradient: LinearGradient(
              colors: [Colors.white, AppTheme.backgroundColor],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس السؤال
              _buildQuestionHeader(question, index),

              SizedBox(height: 20.h),

              // نص السؤال
              _buildQuestionText(question),

              SizedBox(height: 20.h),

              // خيارات الإجابة
              _buildAnswerOptions(question),

              SizedBox(height: 20.h),

              // أزرار السؤال
              _buildQuestionActions(question, isCorrect, isFavorite, hasNote),

              // عرض النتيجة إذا تم التصحيح
              if (isAnswered && _showResults) ...[
                SizedBox(height: 16.h),
                _buildQuestionResult(question, isCorrect),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuestionHeader(Question question, int index) {
    return Row(
      children: [
        // رقم السؤال
        Container(
          width: 40.w,
          height: 40.h,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor,
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Center(
            child: Text(
              '${index + 1}',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),

        SizedBox(width: 12.w),

        // معلومات السؤال
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                question.typeDisplayName,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              Text(
                '${question.points} نقطة',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        // مستوى الصعوبة
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: _getDifficultyColor(
              question.difficulty,
            ).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Text(
            question.difficultyDisplayName,
            style: TextStyle(
              fontSize: 10.sp,
              color: _getDifficultyColor(question.difficulty),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuestionText(Question question) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppTheme.dividerColor),
      ),
      child: Text(
        question.questionText,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildAnswerOptions(Question question) {
    final userAnswer = _userAnswers[question.id];

    return Column(
      children: question.options.map((option) {
        final isSelected = userAnswer?.contains(option) ?? false;

        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: InkWell(
            onTap: () => _selectAnswer(question, option),
            borderRadius: BorderRadius.circular(12.r),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme.primaryColor.withValues(alpha: 0.1)
                    : Colors.transparent,
                border: Border.all(
                  color: isSelected
                      ? AppTheme.primaryColor
                      : AppTheme.dividerColor,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Row(
                children: [
                  Container(
                    width: 20.w,
                    height: 20.h,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected
                          ? AppTheme.primaryColor
                          : Colors.transparent,
                      border: Border.all(
                        color: isSelected
                            ? AppTheme.primaryColor
                            : AppTheme.dividerColor,
                        width: 2,
                      ),
                    ),
                    child: isSelected
                        ? Icon(Icons.check, size: 12.sp, color: Colors.white)
                        : null,
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      option,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: isSelected
                            ? AppTheme.primaryColor
                            : AppTheme.textPrimaryColor,
                        fontWeight: isSelected
                            ? FontWeight.w600
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildQuestionActions(
    Question question,
    bool isCorrect,
    bool isFavorite,
    bool hasNote,
  ) {
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: [
        // زر التصحيح
        _buildActionButton(
          icon: Icons.fact_check,
          label: 'تصحيح',
          color: AppTheme.primaryColor,
          onPressed: () => _checkAnswer(question),
        ),

        // زر المفضلة
        _buildActionButton(
          icon: isFavorite ? Icons.star : Icons.star_outline,
          label: 'مفضلة',
          color: AppTheme.warningColor,
          onPressed: () => _toggleFavorite(question),
        ),

        // زر الملاحظة
        _buildActionButton(
          icon: hasNote ? Icons.note : Icons.note_add,
          label: 'ملاحظة',
          color: AppTheme.secondaryColor,
          onPressed: () => _addNote(question),
        ),

        // زر الإبلاغ
        _buildActionButton(
          icon: Icons.report_outlined,
          label: 'إبلاغ',
          color: AppTheme.errorColor,
          onPressed: () => _reportQuestion(question),
        ),

        // زر التوضيح (إذا كان متوفراً)
        if (question.explanation.isNotEmpty)
          _buildActionButton(
            icon: Icons.lightbulb_outline,
            label: 'توضيح',
            color: AppTheme.accentColor,
            onPressed: () => _showExplanation(question),
          ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16.sp),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withValues(alpha: 0.1),
        foregroundColor: color,
        elevation: 0,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.r),
          side: BorderSide(color: color.withValues(alpha: 0.3)),
        ),
      ),
    );
  }

  Widget _buildQuestionResult(Question question, bool isCorrect) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isCorrect
            ? AppTheme.successColor.withValues(alpha: 0.1)
            : AppTheme.errorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: isCorrect
              ? AppTheme.successColor.withValues(alpha: 0.3)
              : AppTheme.errorColor.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isCorrect ? Icons.check_circle : Icons.cancel,
                color: isCorrect ? AppTheme.successColor : AppTheme.errorColor,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                isCorrect ? 'إجابة صحيحة!' : 'إجابة خاطئة',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: isCorrect
                      ? AppTheme.successColor
                      : AppTheme.errorColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            'الإجابة الصحيحة: ${question.correctAnswers.join(', ')}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر إعادة التعيين
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _resetAnswers,
              icon: Icon(Icons.refresh, size: 16.sp),
              label: Text('إعادة تعيين'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.textSecondaryColor,
                side: BorderSide(color: AppTheme.textSecondaryColor),
                padding: EdgeInsets.symmetric(vertical: 12.h),
              ),
            ),
          ),

          SizedBox(width: 16.w),

          // زر عرض النتائج
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: _showResultsDialog,
              icon: Icon(Icons.analytics, size: 16.sp),
              label: Text('عرض النتائج'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 12.h),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // الدوال المساعدة
  Color _getDifficultyColor(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return AppTheme.successColor;
      case DifficultyLevel.medium:
        return AppTheme.warningColor;
      case DifficultyLevel.hard:
        return AppTheme.errorColor;
    }
  }

  void _selectAnswer(Question question, String option) {
    setState(() {
      if (question.type == QuestionType.multipleChoice ||
          question.type == QuestionType.trueFalse) {
        _userAnswers[question.id] = [option];
      }
    });
  }

  void _checkAnswer(Question question) async {
    final userAnswer = _userAnswers[question.id];
    if (userAnswer == null || userAnswer.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى اختيار إجابة أولاً')));
      return;
    }

    final isCorrect = question.isCorrectAnswers(userAnswer);
    setState(() {
      _questionResults[question.id] = isCorrect;
      _showResults = true;
    });

    // حفظ النتيجة والإجابة في التخزين المحلي
    await StudentDataService.instance.saveQuestionResult(
      question.id,
      isCorrect,
    );
    await StudentDataService.instance.saveUserAnswers(question.id, userAnswer);
  }

  void _checkAllAnswers() async {
    for (final question in _questions) {
      final userAnswer = _userAnswers[question.id];
      if (userAnswer != null && userAnswer.isNotEmpty) {
        final isCorrect = question.isCorrectAnswers(userAnswer);
        _questionResults[question.id] = isCorrect;

        // حفظ النتيجة والإجابة في التخزين المحلي
        await StudentDataService.instance.saveQuestionResult(
          question.id,
          isCorrect,
        );
        await StudentDataService.instance.saveUserAnswers(
          question.id,
          userAnswer,
        );
      }
    }
    setState(() {
      _showResults = true;
    });
    _showResultsDialog();
  }

  void _toggleFavorite(Question question) async {
    final newFavoriteState = !(_favoriteQuestions[question.id] ?? false);

    setState(() {
      _favoriteQuestions[question.id] = newFavoriteState;
    });

    // حفظ في التخزين المحلي
    if (newFavoriteState) {
      await StudentDataService.instance.addToFavorites(question.id);
    } else {
      await StudentDataService.instance.removeFromFavorites(question.id);
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            newFavoriteState
                ? 'تم إضافة السؤال للمفضلة'
                : 'تم إزالة السؤال من المفضلة',
          ),
          backgroundColor: newFavoriteState
              ? AppTheme.successColor
              : AppTheme.textSecondaryColor,
        ),
      );
    }
  }

  void _addNote(Question question) {
    final currentNote = _questionNotes[question.id] ?? '';

    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController(text: currentNote);
        return AlertDialog(
          title: const Text('إضافة ملاحظة'),
          content: TextField(
            controller: controller,
            maxLines: 3,
            decoration: const InputDecoration(
              hintText: 'اكتب ملاحظتك هنا...',
              border: OutlineInputBorder(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final noteText = controller.text.trim();
                setState(() {
                  if (noteText.isNotEmpty) {
                    _questionNotes[question.id] = noteText;
                  } else {
                    _questionNotes.remove(question.id);
                  }
                });

                // حفظ في التخزين المحلي
                await StudentDataService.instance.saveQuestionNote(
                  question.id,
                  noteText,
                );

                if (mounted) {
                  Navigator.pop(context);
                }
              },
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }

  void _reportQuestion(Question question) {
    final telegramUrl =
        'https://t.me/Smart_Test1?text=إبلاغ عن خطأ في السؤال:\nالمادة: ${widget.subject.name}\nالسؤال: ${question.questionText}';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إبلاغ عن خطأ'),
        content: const Text('سيتم فتح تطبيق تلغرام لإرسال الإبلاغ'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              launchUrl(Uri.parse(telegramUrl));
            },
            child: const Text('فتح تلغرام'),
          ),
        ],
      ),
    );
  }

  void _showExplanation(Question question) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('توضيح الحل'),
        content: Text(question.explanation),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _resetAnswers() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين'),
        content: const Text('هل تريد مسح جميع الإجابات والبدء من جديد؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _userAnswers.clear();
                _questionResults.clear();
                _showResults = false;
              });
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('مسح الكل'),
          ),
        ],
      ),
    );
  }

  void _showResultsDialog() {
    final percentage = _totalQuestions > 0
        ? (_correctAnswers / _totalQuestions * 100)
        : 0;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نتائج الأسئلة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${percentage.toInt()}%',
              style: TextStyle(
                fontSize: 48.sp,
                fontWeight: FontWeight.bold,
                color: percentage >= 70
                    ? AppTheme.successColor
                    : AppTheme.errorColor,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  children: [
                    Text(
                      _totalQuestions.toString(),
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    Text('الكل', style: TextStyle(fontSize: 12.sp)),
                  ],
                ),
                Column(
                  children: [
                    Text(
                      _correctAnswers.toString(),
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.successColor,
                      ),
                    ),
                    Text('صحيح', style: TextStyle(fontSize: 12.sp)),
                  ],
                ),
                Column(
                  children: [
                    Text(
                      _wrongAnswers.toString(),
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.errorColor,
                      ),
                    ),
                    Text('خاطئ', style: TextStyle(fontSize: 12.sp)),
                  ],
                ),
              ],
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
