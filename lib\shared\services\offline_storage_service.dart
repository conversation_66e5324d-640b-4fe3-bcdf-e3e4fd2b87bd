import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/question_model.dart';
import '../models/subject_model.dart';
import '../models/unit_model.dart';
import '../models/lesson_model.dart';

/// خدمة التخزين المحلي للأسئلة والمحتوى
class OfflineStorageService {
  static final OfflineStorageService _instance =
      OfflineStorageService._internal();
  static OfflineStorageService get instance => _instance;
  OfflineStorageService._internal();

  static const String _questionsKey = 'offline_questions';
  static const String _subjectsKey = 'offline_subjects';
  static const String _unitsKey = 'offline_units';
  static const String _lessonsKey = 'offline_lessons';
  static const String _downloadedSubjectsKey = 'downloaded_subjects';
  static const String _lastSyncKey = 'last_sync_time';

  /// حفظ الأسئلة محلياً
  Future<void> saveQuestions(List<Question> questions) async {
    final prefs = await SharedPreferences.getInstance();
    final questionsJson = questions
        .map(
          (q) => {
            'id': q.id,
            'questionText': q.questionText,
            'options': q.options,
            'correctAnswers': q.correctAnswers,
            'explanation': q.explanation,
            'subjectId': q.subjectId,
            'unitId': q.unitId,
            'lessonId': q.lessonId,
            'difficulty': q.difficulty.toString().split('.').last,
            'type': q.type.toString().split('.').last,
            'points': q.points,
            'imageUrl': q.imageUrl,
            'metadata': q.metadata,
            'createdByAdminId': q.createdByAdminId,
            'isActive': q.isActive,
            'isCourseQuestion': q.isCourseQuestion,
            'createdAt': q.createdAt.toIso8601String(),
            'updatedAt': q.updatedAt.toIso8601String(),
          },
        )
        .toList();
    await prefs.setString(_questionsKey, jsonEncode(questionsJson));
  }

  /// تحميل الأسئلة المحفوظة محلياً
  Future<List<Question>> getOfflineQuestions() async {
    final prefs = await SharedPreferences.getInstance();
    final questionsJson = prefs.getString(_questionsKey);
    if (questionsJson == null) return [];

    try {
      final questionsList = jsonDecode(questionsJson) as List;
      return questionsList
          .map(
            (q) => Question(
              id: q['id'],
              questionText: q['questionText'],
              options: List<String>.from(q['options']),
              correctAnswers: List<String>.from(q['correctAnswers']),
              explanation: q['explanation'],
              subjectId: q['subjectId'],
              unitId: q['unitId'],
              lessonId: q['lessonId'],
              difficulty: DifficultyLevel.values.firstWhere(
                (d) => d.toString().split('.').last == q['difficulty'],
                orElse: () => DifficultyLevel.medium,
              ),
              type: QuestionType.values.firstWhere(
                (t) => t.toString().split('.').last == q['type'],
                orElse: () => QuestionType.multipleChoice,
              ),
              points: q['points'] ?? 1,
              imageUrl: q['imageUrl'] ?? '',
              metadata: Map<String, dynamic>.from(q['metadata'] ?? {}),
              createdByAdminId: q['createdByAdminId'] ?? '',
              isActive: q['isActive'] ?? true,
              isCourseQuestion: q['isCourseQuestion'] ?? false,
              createdAt: DateTime.parse(q['createdAt']),
              updatedAt: DateTime.parse(q['updatedAt']),
            ),
          )
          .toList();
    } catch (e) {
      print('خطأ في تحميل الأسئلة المحفوظة: $e');
      return [];
    }
  }

  /// حفظ المواد محلياً
  Future<void> saveSubjects(List<Subject> subjects) async {
    final prefs = await SharedPreferences.getInstance();
    final subjectsJson = subjects
        .map(
          (s) => {
            'id': s.id,
            'name': s.name,
            'description': s.description,
            'iconUrl': s.iconUrl,
            'color': s.color,
            'isActive': s.isActive,
            'createdAt': s.createdAt.toIso8601String(),
            'updatedAt': s.updatedAt.toIso8601String(),
          },
        )
        .toList();
    await prefs.setString(_subjectsKey, jsonEncode(subjectsJson));
  }

  /// تحميل المواد المحفوظة محلياً
  Future<List<Subject>> getOfflineSubjects() async {
    final prefs = await SharedPreferences.getInstance();
    final subjectsJson = prefs.getString(_subjectsKey);
    if (subjectsJson == null) return [];

    try {
      final subjectsList = jsonDecode(subjectsJson) as List;
      return subjectsList
          .map(
            (s) => Subject(
              id: s['id'],
              name: s['name'],
              description: s['description'],
              iconUrl: s['iconUrl'] ?? '',
              color: s['color'],
              isActive: s['isActive'] ?? true,
              createdAt: DateTime.parse(s['createdAt']),
              updatedAt: DateTime.parse(s['updatedAt']),
            ),
          )
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// حفظ الوحدات محلياً
  Future<void> saveUnits(List<Unit> units) async {
    final prefs = await SharedPreferences.getInstance();
    final unitsJson = units
        .map(
          (u) => {
            'id': u.id,
            'name': u.name,
            'description': u.description,
            'subjectId': u.subjectId,
            'order': u.order,
            'iconUrl': u.iconUrl,
            'color': u.color,
            'isActive': u.isActive,
            'createdByAdminId': u.createdByAdminId,
            'createdAt': u.createdAt.toIso8601String(),
            'updatedAt': u.updatedAt.toIso8601String(),
          },
        )
        .toList();
    await prefs.setString(_unitsKey, jsonEncode(unitsJson));
  }

  /// تحميل الوحدات المحفوظة محلياً
  Future<List<Unit>> getOfflineUnits() async {
    final prefs = await SharedPreferences.getInstance();
    final unitsJson = prefs.getString(_unitsKey);
    if (unitsJson == null) return [];

    try {
      final unitsList = jsonDecode(unitsJson) as List;
      return unitsList
          .map(
            (u) => Unit(
              id: u['id'],
              name: u['name'],
              description: u['description'],
              subjectId: u['subjectId'],
              order: u['order'] ?? 0,
              iconUrl: u['iconUrl'] ?? '',
              color: u['color'] ?? '',
              isActive: u['isActive'] ?? true,
              createdByAdminId: u['createdByAdminId'] ?? '',
              createdAt: DateTime.parse(u['createdAt']),
              updatedAt: DateTime.parse(u['updatedAt']),
            ),
          )
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// حفظ الدروس محلياً
  Future<void> saveLessons(List<Lesson> lessons) async {
    final prefs = await SharedPreferences.getInstance();
    final lessonsJson = lessons.map((l) => l.toMap()).toList();
    await prefs.setString(_lessonsKey, jsonEncode(lessonsJson));
  }

  /// تحميل الدروس المحفوظة محلياً
  Future<List<Lesson>> getOfflineLessons() async {
    final prefs = await SharedPreferences.getInstance();
    final lessonsJson = prefs.getString(_lessonsKey);
    if (lessonsJson == null) return [];

    final lessonsList = jsonDecode(lessonsJson) as List;
    return lessonsList.map((l) => Lesson.fromMap(l)).toList();
  }

  /// إضافة مادة للمواد المحملة
  Future<void> addDownloadedSubject(String subjectId) async {
    final prefs = await SharedPreferences.getInstance();
    final downloadedSubjects = await getDownloadedSubjects();
    if (!downloadedSubjects.contains(subjectId)) {
      downloadedSubjects.add(subjectId);
      await prefs.setStringList(_downloadedSubjectsKey, downloadedSubjects);
    }
  }

  /// إزالة مادة من المواد المحملة
  Future<void> removeDownloadedSubject(String subjectId) async {
    final prefs = await SharedPreferences.getInstance();
    final downloadedSubjects = await getDownloadedSubjects();
    downloadedSubjects.remove(subjectId);
    await prefs.setStringList(_downloadedSubjectsKey, downloadedSubjects);
  }

  /// الحصول على قائمة المواد المحملة
  Future<List<String>> getDownloadedSubjects() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_downloadedSubjectsKey) ?? [];
  }

  /// فحص ما إذا كانت المادة محملة
  Future<bool> isSubjectDownloaded(String subjectId) async {
    final downloadedSubjects = await getDownloadedSubjects();
    return downloadedSubjects.contains(subjectId);
  }

  /// تحديث وقت آخر مزامنة
  Future<void> updateLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_lastSyncKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// الحصول على وقت آخر مزامنة
  Future<DateTime?> getLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final timestamp = prefs.getInt(_lastSyncKey);
    return timestamp != null
        ? DateTime.fromMillisecondsSinceEpoch(timestamp)
        : null;
  }

  /// تحميل أسئلة مادة معينة محلياً
  Future<List<Question>> getOfflineQuestionsBySubject(String subjectId) async {
    final allQuestions = await getOfflineQuestions();
    return allQuestions.where((q) => q.subjectId == subjectId).toList();
  }

  /// تحميل أسئلة وحدة معينة محلياً
  Future<List<Question>> getOfflineQuestionsByUnit(String unitId) async {
    final allQuestions = await getOfflineQuestions();
    return allQuestions.where((q) => q.unitId == unitId).toList();
  }

  /// تحميل أسئلة درس معين محلياً
  Future<List<Question>> getOfflineQuestionsByLesson(String lessonId) async {
    final allQuestions = await getOfflineQuestions();
    return allQuestions.where((q) => q.lessonId == lessonId).toList();
  }

  /// تحميل وحدات مادة معينة محلياً
  Future<List<Unit>> getOfflineUnitsBySubject(String subjectId) async {
    final allUnits = await getOfflineUnits();
    return allUnits.where((u) => u.subjectId == subjectId).toList();
  }

  /// تحميل دروس وحدة معينة محلياً
  Future<List<Lesson>> getOfflineLessonsByUnit(String unitId) async {
    final allLessons = await getOfflineLessons();
    return allLessons.where((l) => l.unitId == unitId).toList();
  }

  /// حساب حجم البيانات المحفوظة
  Future<Map<String, int>> getStorageSize() async {
    final prefs = await SharedPreferences.getInstance();

    final questionsSize = (prefs.getString(_questionsKey) ?? '').length;
    final subjectsSize = (prefs.getString(_subjectsKey) ?? '').length;
    final unitsSize = (prefs.getString(_unitsKey) ?? '').length;
    final lessonsSize = (prefs.getString(_lessonsKey) ?? '').length;

    return {
      'questions': questionsSize,
      'subjects': subjectsSize,
      'units': unitsSize,
      'lessons': lessonsSize,
      'total': questionsSize + subjectsSize + unitsSize + lessonsSize,
    };
  }

  /// مسح جميع البيانات المحفوظة
  Future<void> clearAllOfflineData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_questionsKey);
    await prefs.remove(_subjectsKey);
    await prefs.remove(_unitsKey);
    await prefs.remove(_lessonsKey);
    await prefs.remove(_downloadedSubjectsKey);
    await prefs.remove(_lastSyncKey);
  }

  /// مسح بيانات مادة معينة
  Future<void> clearSubjectData(String subjectId) async {
    // إزالة الأسئلة
    final questions = await getOfflineQuestions();
    final filteredQuestions = questions
        .where((q) => q.subjectId != subjectId)
        .toList();
    await saveQuestions(filteredQuestions);

    // إزالة الوحدات
    final units = await getOfflineUnits();
    final filteredUnits = units.where((u) => u.subjectId != subjectId).toList();
    await saveUnits(filteredUnits);

    // إزالة الدروس (بناءً على الوحدات المحذوفة)
    final unitIds = units
        .where((u) => u.subjectId == subjectId)
        .map((u) => u.id)
        .toList();
    final lessons = await getOfflineLessons();
    final filteredLessons = lessons
        .where((l) => !unitIds.contains(l.unitId))
        .toList();
    await saveLessons(filteredLessons);

    // إزالة من قائمة المواد المحملة
    await removeDownloadedSubject(subjectId);
  }

  /// فحص ما إذا كانت البيانات متاحة محلياً
  Future<bool> hasOfflineData() async {
    final questions = await getOfflineQuestions();
    final subjects = await getOfflineSubjects();
    return questions.isNotEmpty && subjects.isNotEmpty;
  }

  /// الحصول على إحصائيات التخزين المحلي
  Future<Map<String, dynamic>> getOfflineStats() async {
    final questions = await getOfflineQuestions();
    final subjects = await getOfflineSubjects();
    final units = await getOfflineUnits();
    final lessons = await getOfflineLessons();
    final downloadedSubjects = await getDownloadedSubjects();
    final lastSync = await getLastSyncTime();
    final storageSize = await getStorageSize();

    return {
      'questionsCount': questions.length,
      'subjectsCount': subjects.length,
      'unitsCount': units.length,
      'lessonsCount': lessons.length,
      'downloadedSubjectsCount': downloadedSubjects.length,
      'lastSyncTime': lastSync?.toIso8601String(),
      'storageSizeKB': (storageSize['total']! / 1024).round(),
    };
  }
}
