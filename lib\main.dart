import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options_student.dart';
import 'firebase_options_admin.dart' as admin_options;
import 'app.dart';
import 'flavors.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تحديد الـ flavor بناءً على متغير البيئة
  const flavor = String.fromEnvironment('FLAVOR', defaultValue: 'student');

  if (flavor == 'admin') {
    F.appFlavor = Flavor.admin;
    await Firebase.initializeApp(
      options: admin_options.DefaultFirebaseOptions.currentPlatform,
    );
  } else {
    F.appFlavor = Flavor.student;
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  }

  runApp(const MyApp());
}
