enum CodeStatus {
  active,    // الكود نشط ولم يُستخدم
  used,      // الكود تم استخدامه
  expired,   // الكود منتهي الصلاحية
  disabled,  // الكود معطل من الأدمن
}

class SubscriptionCode {
  final String id;
  final String code;
  final List<String> subjectIds; // المواد المرتبطة بهذا الكود
  final CodeStatus status;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final String? usedByDeviceId; // معرف الجهاز الذي استخدم الكود
  final DateTime? usedAt; // تاريخ الاستخدام
  final String createdByAdminId; // معرف الأدمن الذي أنشأ الكود
  final String? notes; // ملاحظات إضافية

  SubscriptionCode({
    required this.id,
    required this.code,
    required this.subjectIds,
    this.status = CodeStatus.active,
    required this.createdAt,
    this.expiresAt,
    this.usedByDeviceId,
    this.usedAt,
    required this.createdByAdminId,
    this.notes,
  });

  factory SubscriptionCode.fromMap(Map<String, dynamic> map) {
    return SubscriptionCode(
      id: map['id'] ?? '',
      code: map['code'] ?? '',
      subjectIds: List<String>.from(map['subjectIds'] ?? []),
      status: CodeStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => CodeStatus.active,
      ),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      expiresAt: map['expiresAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['expiresAt'])
          : null,
      usedByDeviceId: map['usedByDeviceId'],
      usedAt: map['usedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['usedAt'])
          : null,
      createdByAdminId: map['createdByAdminId'] ?? '',
      notes: map['notes'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'subjectIds': subjectIds,
      'status': status.name,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'expiresAt': expiresAt?.millisecondsSinceEpoch,
      'usedByDeviceId': usedByDeviceId,
      'usedAt': usedAt?.millisecondsSinceEpoch,
      'createdByAdminId': createdByAdminId,
      'notes': notes,
    };
  }

  bool get isValid {
    if (status != CodeStatus.active) return false;
    if (expiresAt != null && DateTime.now().isAfter(expiresAt!)) return false;
    return true;
  }

  bool get isUsed => status == CodeStatus.used && usedByDeviceId != null;

  SubscriptionCode copyWith({
    String? id,
    String? code,
    List<String>? subjectIds,
    CodeStatus? status,
    DateTime? createdAt,
    DateTime? expiresAt,
    String? usedByDeviceId,
    DateTime? usedAt,
    String? createdByAdminId,
    String? notes,
  }) {
    return SubscriptionCode(
      id: id ?? this.id,
      code: code ?? this.code,
      subjectIds: subjectIds ?? this.subjectIds,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      usedByDeviceId: usedByDeviceId ?? this.usedByDeviceId,
      usedAt: usedAt ?? this.usedAt,
      createdByAdminId: createdByAdminId ?? this.createdByAdminId,
      notes: notes ?? this.notes,
    );
  }
}
