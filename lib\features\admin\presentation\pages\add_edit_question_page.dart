import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/question_model.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/unit_model.dart';
import '../../../../shared/services/content_service.dart';
import '../../../../shared/services/exam_service.dart';

class AddEditQuestionPage extends StatefulWidget {
  final Question? question;
  final List<Subject> subjects;
  final bool isCourse;
  final VoidCallback? onQuestionSaved;

  const AddEditQuestionPage({
    super.key,
    this.question,
    required this.subjects,
    required this.isCourse,
    this.onQuestionSaved,
  });

  @override
  State<AddEditQuestionPage> createState() => _AddEditQuestionPageState();
}

class _AddEditQuestionPageState extends State<AddEditQuestionPage> {
  final _formKey = GlobalKey<FormState>();
  final _questionController = TextEditingController();
  final _explanationController = TextEditingController();
  final _pointsController = TextEditingController();

  List<TextEditingController> _optionControllers = [];
  List<Unit> _units = [];
  List<Lesson> _lessons = [];

  String? _selectedSubjectId;
  String? _selectedUnitId;
  String? _selectedLessonId;
  QuestionType _selectedType = QuestionType.multipleChoice;
  DifficultyLevel _selectedDifficulty = DifficultyLevel.medium;
  int _correctAnswerIndex = 0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.question != null) {
      // تعديل سؤال موجود
      final question = widget.question!;
      _questionController.text = question.questionText;
      _explanationController.text = question.explanation;
      _pointsController.text = question.points.toString();
      _selectedSubjectId = question.subjectId;
      _selectedUnitId = question.unitId.isEmpty ? null : question.unitId;
      _selectedLessonId = question.lessonId.isEmpty ? null : question.lessonId;
      _selectedType = question.type;
      _selectedDifficulty = question.difficulty;
      _correctAnswerIndex = question.options.indexOf(
        question.correctAnswers.first,
      );

      // إعداد الخيارات
      _optionControllers = question.options
          .map((option) => TextEditingController(text: option))
          .toList();
    } else {
      // إضافة سؤال جديد
      _pointsController.text = '1';
      _addOption();
      _addOption();
      _addOption();
      _addOption();
    }

    if (_selectedSubjectId != null) {
      _loadUnits(_selectedSubjectId!);
    }
  }

  @override
  void dispose() {
    _questionController.dispose();
    _explanationController.dispose();
    _pointsController.dispose();
    for (var controller in _optionControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _loadUnits(String subjectId) async {
    try {
      final units = await ContentService.instance.getSubjectUnits(subjectId);
      setState(() {
        _units = units;
        if (_selectedUnitId != null &&
            !units.any((unit) => unit.id == _selectedUnitId)) {
          _selectedUnitId = null;
          _selectedLessonId = null;
          _lessons.clear();
        }
      });

      if (_selectedUnitId != null) {
        _loadLessons(_selectedUnitId!);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الوحدات: $e')));
      }
    }
  }

  Future<void> _loadLessons(String unitId) async {
    try {
      final lessons = await ContentService.instance.getUnitLessons(unitId);
      setState(() {
        _lessons = lessons;
        if (_selectedLessonId != null &&
            !lessons.any((lesson) => lesson.id == _selectedLessonId)) {
          _selectedLessonId = null;
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الدروس: $e')));
      }
    }
  }

  void _addOption() {
    setState(() {
      _optionControllers.add(TextEditingController());
    });
  }

  void _removeOption(int index) {
    if (_optionControllers.length > 2) {
      setState(() {
        _optionControllers[index].dispose();
        _optionControllers.removeAt(index);
        if (_correctAnswerIndex >= _optionControllers.length) {
          _correctAnswerIndex = _optionControllers.length - 1;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          widget.question != null ? 'تعديل السؤال' : 'إضافة سؤال جديد',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: widget.isCourse
                ? AppTheme.secondaryGradient
                : AppTheme.primaryGradient,
          ),
        ),
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveQuestion,
              child: Text(
                'حفظ',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات أساسية
              _buildBasicInfoSection(),

              SizedBox(height: 24.h),

              // نص السؤال
              _buildQuestionTextSection(),

              SizedBox(height: 24.h),

              // الخيارات
              _buildOptionsSection(),

              SizedBox(height: 24.h),

              // الشرح
              _buildExplanationSection(),

              SizedBox(height: 32.h),

              // أزرار الحفظ والإلغاء
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الأساسية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            // المادة
            DropdownButtonFormField<String>(
              value: _selectedSubjectId,
              decoration: const InputDecoration(
                labelText: 'المادة *',
                border: OutlineInputBorder(),
              ),
              items: widget.subjects
                  .map(
                    (subject) => DropdownMenuItem(
                      value: subject.id,
                      child: Text(subject.name),
                    ),
                  )
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedSubjectId = value;
                  _selectedUnitId = null;
                  _selectedLessonId = null;
                  _units.clear();
                  _lessons.clear();
                });
                if (value != null) {
                  _loadUnits(value);
                }
              },
              validator: (value) => value == null ? 'يرجى اختيار المادة' : null,
            ),

            SizedBox(height: 16.h),

            // الوحدة
            DropdownButtonFormField<String>(
              value: _selectedUnitId,
              decoration: const InputDecoration(
                labelText: 'الوحدة',
                border: OutlineInputBorder(),
              ),
              items: _units
                  .map(
                    (unit) => DropdownMenuItem(
                      value: unit.id,
                      child: Text(unit.name),
                    ),
                  )
                  .toList(),
              onChanged: _selectedSubjectId == null
                  ? null
                  : (value) {
                      setState(() {
                        _selectedUnitId = value;
                        _selectedLessonId = null;
                        _lessons.clear();
                      });
                      if (value != null) {
                        _loadLessons(value);
                      }
                    },
            ),

            SizedBox(height: 16.h),

            // الدرس
            DropdownButtonFormField<String>(
              value: _selectedLessonId,
              decoration: const InputDecoration(
                labelText: 'الدرس',
                border: OutlineInputBorder(),
              ),
              items: _lessons
                  .map(
                    (lesson) => DropdownMenuItem(
                      value: lesson.id,
                      child: Text(lesson.name),
                    ),
                  )
                  .toList(),
              onChanged: _selectedUnitId == null
                  ? null
                  : (value) {
                      setState(() {
                        _selectedLessonId = value;
                      });
                    },
            ),

            SizedBox(height: 16.h),

            Row(
              children: [
                // نوع السؤال
                Expanded(
                  child: DropdownButtonFormField<QuestionType>(
                    value: _selectedType,
                    decoration: const InputDecoration(
                      labelText: 'نوع السؤال',
                      border: OutlineInputBorder(),
                    ),
                    items: QuestionType.values
                        .map(
                          (type) => DropdownMenuItem(
                            value: type,
                            child: Text(_getQuestionTypeDisplayName(type)),
                          ),
                        )
                        .toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedType = value!;
                      });
                    },
                  ),
                ),

                SizedBox(width: 16.w),

                // مستوى الصعوبة
                Expanded(
                  child: DropdownButtonFormField<DifficultyLevel>(
                    value: _selectedDifficulty,
                    decoration: const InputDecoration(
                      labelText: 'مستوى الصعوبة',
                      border: OutlineInputBorder(),
                    ),
                    items: DifficultyLevel.values
                        .map(
                          (level) => DropdownMenuItem(
                            value: level,
                            child: Text(_getDifficultyDisplayName(level)),
                          ),
                        )
                        .toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedDifficulty = value!;
                      });
                    },
                  ),
                ),

                SizedBox(width: 16.w),

                // النقاط
                SizedBox(
                  width: 80.w,
                  child: TextFormField(
                    controller: _pointsController,
                    decoration: const InputDecoration(
                      labelText: 'النقاط',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      if (int.tryParse(value) == null || int.parse(value) < 1) {
                        return 'رقم صحيح';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuestionTextSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نص السؤال',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            TextFormField(
              controller: _questionController,
              decoration: const InputDecoration(
                labelText: 'اكتب السؤال هنا *',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: 4,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى كتابة نص السؤال';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionsSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'الخيارات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _addOption,
                  icon: const Icon(Icons.add),
                  tooltip: 'إضافة خيار',
                ),
              ],
            ),

            SizedBox(height: 16.h),

            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _optionControllers.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: EdgeInsets.only(bottom: 12.h),
                  child: Row(
                    children: [
                      // راديو للإجابة الصحيحة
                      Radio<int>(
                        value: index,
                        groupValue: _correctAnswerIndex,
                        onChanged: (value) {
                          setState(() {
                            _correctAnswerIndex = value!;
                          });
                        },
                      ),

                      // نص الخيار
                      Expanded(
                        child: TextFormField(
                          controller: _optionControllers[index],
                          decoration: InputDecoration(
                            labelText: 'الخيار ${index + 1}',
                            border: const OutlineInputBorder(),
                            suffixIcon: _optionControllers.length > 2
                                ? IconButton(
                                    onPressed: () => _removeOption(index),
                                    icon: const Icon(
                                      Icons.delete,
                                      color: Colors.red,
                                    ),
                                  )
                                : null,
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى كتابة نص الخيار';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),

            if (_optionControllers.length < 6)
              Center(
                child: TextButton.icon(
                  onPressed: _addOption,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة خيار آخر'),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildExplanationSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الشرح (اختياري)',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            TextFormField(
              controller: _explanationController,
              decoration: const InputDecoration(
                labelText: 'شرح الإجابة الصحيحة',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.textSecondaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Text(
              'إلغاء',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
          ),
        ),

        SizedBox(width: 16.w),

        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveQuestion,
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.isCourse
                  ? AppTheme.accentColor
                  : AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    widget.question != null ? 'تحديث السؤال' : 'إضافة السؤال',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Future<void> _saveQuestion() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من وجود خيارات صحيحة
    final options = _optionControllers.map((c) => c.text.trim()).toList();
    if (options.any((option) => option.isEmpty)) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى ملء جميع الخيارات')));
      return;
    }

    setState(() => _isLoading = true);

    try {
      final question = Question(
        id:
            widget.question?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        questionText: _questionController.text.trim(),
        options: options,
        correctAnswers: [options[_correctAnswerIndex]],
        explanation: _explanationController.text.trim(),
        type: _selectedType,
        difficulty: _selectedDifficulty,
        points: int.parse(_pointsController.text),
        subjectId: _selectedSubjectId!,
        unitId: _selectedUnitId ?? '',
        lessonId: _selectedLessonId ?? '',
        isCourseQuestion: widget.isCourse,
        isActive: true,
        imageUrl: '',
        metadata: {},
        createdByAdminId: 'admin',
        createdAt: widget.question?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.question != null) {
        await ExamService.instance.updateQuestion(question);
      } else {
        await ExamService.instance.createQuestion(question);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.question != null
                  ? 'تم تحديث السؤال بنجاح'
                  : 'تم إضافة السؤال بنجاح',
            ),
            backgroundColor: AppTheme.successColor,
          ),
        );

        widget.onQuestionSaved?.call();
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حفظ السؤال: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _getQuestionTypeDisplayName(QuestionType type) {
    switch (type) {
      case QuestionType.multipleChoice:
        return 'اختيار من متعدد';
      case QuestionType.trueFalse:
        return 'صح أم خطأ';
      case QuestionType.shortAnswer:
        return 'إجابة قصيرة';
      case QuestionType.essay:
        return 'مقال';
      case QuestionType.matching:
        return 'مطابقة';
      case QuestionType.fillInTheBlank:
        return 'ملء الفراغات';
    }
  }

  String _getDifficultyDisplayName(DifficultyLevel level) {
    switch (level) {
      case DifficultyLevel.easy:
        return 'سهل';
      case DifficultyLevel.medium:
        return 'متوسط';
      case DifficultyLevel.hard:
        return 'صعب';
    }
  }
}
