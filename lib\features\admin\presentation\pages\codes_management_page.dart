import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subscription_code_model.dart';
import '../../../../shared/services/activation_service.dart';
import 'generate_codes_page.dart';

class CodesManagementPage extends StatefulWidget {
  const CodesManagementPage({super.key});

  @override
  State<CodesManagementPage> createState() => _CodesManagementPageState();
}

class _CodesManagementPageState extends State<CodesManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  List<SubscriptionCode> _activeCodes = [];
  List<SubscriptionCode> _usedCodes = [];
  List<SubscriptionCode> _expiredCodes = [];

  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadCodes();
  }

  void _loadData() {
    // إعادة تحميل البيانات
    _loadCodes();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadCodes() async {
    try {
      setState(() => _isLoading = true);

      final allCodesSnapshot = await FirebaseFirestore.instance
          .collection('activation_codes')
          .orderBy('createdAt', descending: true)
          .get();

      final allCodes = allCodesSnapshot.docs
          .map((doc) => ActivationCode.fromMap(doc.data()))
          .toList();

      final now = DateTime.now();

      setState(() {
        _activeCodes = allCodes
            .where((code) => !code.isUsed && code.expiryDate.isAfter(now))
            .toList();
        _usedCodes = allCodes.where((code) => code.isUsed).toList();
        _expiredCodes = allCodes
            .where((code) => !code.isUsed && code.expiryDate.isBefore(now))
            .toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الأكواد: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'إدارة أكواد التفعيل',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.accentGradient),
        ),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showGenerateCodesDialog,
            icon: const Icon(Icons.add),
            tooltip: 'إنشاء أكواد جديدة',
          ),
          IconButton(
            onPressed: _showBulkActionsDialog,
            icon: const Icon(Icons.more_vert),
            tooltip: 'إجراءات متقدمة',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
          tabs: [
            Tab(
              icon: const Icon(Icons.check_circle),
              text: 'نشطة (${_activeCodes.length})',
            ),
            Tab(
              icon: const Icon(Icons.used),
              text: 'مستخدمة (${_usedCodes.length})',
            ),
            Tab(
              icon: const Icon(Icons.expired),
              text: 'منتهية (${_expiredCodes.length})',
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // شريط البحث
                _buildSearchBar(),

                // محتوى التبويبات
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildCodesTab(_activeCodes, 'active'),
                      _buildCodesTab(_usedCodes, 'used'),
                      _buildCodesTab(_expiredCodes, 'expired'),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'البحث في الأكواد...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: AppTheme.backgroundColor,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 12.h,
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value.toLowerCase();
          });
        },
      ),
    );
  }

  Widget _buildCodesTab(List<SubscriptionCode> codes, String type) {
    // تطبيق البحث
    List<SubscriptionCode> filteredCodes = codes.where((code) {
      if (_searchQuery.isEmpty) return true;
      return code.code.toLowerCase().contains(_searchQuery) ||
          (code.usedByDeviceId?.toLowerCase().contains(_searchQuery) ??
              false) ||
          (code.notes?.toLowerCase().contains(_searchQuery) ?? false);
    }).toList();

    if (filteredCodes.isEmpty) {
      return _buildEmptyState(type);
    }

    return RefreshIndicator(
      onRefresh: _loadCodes,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: filteredCodes.length,
        itemBuilder: (context, index) {
          final code = filteredCodes[index];
          return _buildCodeCard(code, type);
        },
      ),
    );
  }

  Widget _buildEmptyState(String type) {
    String title, subtitle;
    IconData icon;

    switch (type) {
      case 'active':
        title = 'لا توجد أكواد نشطة';
        subtitle = 'اضغط على + لإنشاء أكواد جديدة';
        icon = Icons.check_circle_outline;
        break;
      case 'used':
        title = 'لا توجد أكواد مستخدمة';
        subtitle = 'الأكواد المستخدمة ستظهر هنا';
        icon = Icons.check_circle;
        break;
      case 'expired':
        title = 'لا توجد أكواد منتهية';
        subtitle = 'الأكواد المنتهية الصلاحية ستظهر هنا';
        icon = Icons.expired;
        break;
      default:
        title = 'لا توجد أكواد';
        subtitle = '';
        icon = Icons.code;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 80.sp, color: AppTheme.textSecondaryColor),
          SizedBox(height: 16.h),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          if (subtitle.isNotEmpty) ...[
            SizedBox(height: 8.h),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCodeCard(SubscriptionCode code, String type) {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (type) {
      case 'active':
        statusColor = AppTheme.successColor;
        statusText = 'نشط';
        statusIcon = Icons.check_circle;
        break;
      case 'used':
        statusColor = AppTheme.primaryColor;
        statusText = 'مستخدم';
        statusIcon = Icons.check;
        break;
      case 'expired':
        statusColor = AppTheme.errorColor;
        statusText = 'منتهي';
        statusIcon = Icons.expired;
        break;
      default:
        statusColor = AppTheme.textSecondaryColor;
        statusText = 'غير محدد';
        statusIcon = Icons.help;
    }

    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              statusColor.withValues(alpha: 0.1),
              statusColor.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس الكود
              Row(
                children: [
                  // حالة الكود
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(statusIcon, size: 12.sp, color: Colors.white),
                        SizedBox(width: 4.w),
                        Text(
                          statusText,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(width: 8.w),

                  // نوع الكود
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.accentColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      code.expiresAt != null
                          ? '${code.expiresAt!.difference(code.createdAt).inDays} يوم'
                          : 'بلا انتهاء',
                      style: TextStyle(
                        color: AppTheme.accentColor,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                  const Spacer(),

                  // أزرار الإجراءات
                  PopupMenuButton<String>(
                    onSelected: (action) => _handleCodeAction(action, code),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'copy',
                        child: Row(
                          children: [
                            Icon(Icons.copy),
                            SizedBox(width: 8),
                            Text('نسخ الكود'),
                          ],
                        ),
                      ),
                      if (type == 'active') ...[
                        const PopupMenuItem(
                          value: 'deactivate',
                          child: Row(
                            children: [
                              Icon(Icons.block),
                              SizedBox(width: 8),
                              Text('إلغاء تفعيل'),
                            ],
                          ),
                        ),
                      ],
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // الكود
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: AppTheme.backgroundColor,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(
                    color: AppTheme.textSecondaryColor.withValues(alpha: 0.2),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        code.code,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'monospace',
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _copyToClipboard(code.code),
                      icon: Icon(Icons.copy, size: 20.sp),
                      tooltip: 'نسخ',
                    ),
                  ],
                ),
              ),

              SizedBox(height: 12.h),

              // معلومات إضافية
              _buildInfoRow('تاريخ الإنشاء', _formatDate(code.createdAt)),
              SizedBox(height: 4.h),
              _buildInfoRow(
                'تاريخ الانتهاء',
                code.expiresAt != null
                    ? _formatDate(code.expiresAt!)
                    : 'بلا انتهاء',
              ),
              if (code.isUsed) ...[
                SizedBox(height: 4.h),
                _buildInfoRow('تاريخ الاستخدام', _formatDate(code.usedAt!)),
                SizedBox(height: 4.h),
                _buildInfoRow('الجهاز', code.usedByDeviceId ?? 'غير محدد'),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      children: [
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 12.sp,
            color: AppTheme.textSecondaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(fontSize: 12.sp, color: AppTheme.textPrimaryColor),
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _copyToClipboard(String text) {
    // سنحتاج إضافة clipboard package
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم نسخ الكود'),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  void _handleCodeAction(String action, SubscriptionCode code) {
    switch (action) {
      case 'copy':
        _copyToClipboard(code.code);
        break;
      case 'deactivate':
        _deactivateCode(code);
        break;
      case 'delete':
        _deleteCode(code);
        break;
    }
  }

  void _deactivateCode(SubscriptionCode code) async {
    // تنفيذ إلغاء التفعيل
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تم إلغاء تفعيل الكود')));
  }

  void _deleteCode(SubscriptionCode code) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا الكود؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await FirebaseFirestore.instance
            .collection('activation_codes')
            .doc(code.id)
            .delete();

        _loadCodes();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الكود بنجاح'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('خطأ في الحذف: $e')));
        }
      }
    }
  }

  void _showGenerateCodesDialog() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GenerateCodesPage(onCodesGenerated: _loadData),
      ),
    );
  }

  void _showBulkActionsDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('الإجراءات المتقدمة - قيد التطوير')),
    );
  }
}
