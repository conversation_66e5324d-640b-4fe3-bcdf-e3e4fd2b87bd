import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/exam_model.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/services/exam_service.dart';
import '../../../../shared/services/subscription_service.dart';
import '../widgets/exam_card_widget.dart';
import 'exam_taking_page.dart';

class ExamsPage extends StatefulWidget {
  final String? subjectId;

  const ExamsPage({super.key, this.subjectId});

  @override
  State<ExamsPage> createState() => _ExamsPageState();
}

class _ExamsPageState extends State<ExamsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String? _selectedSubjectId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'الاختبارات',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
        ),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w600),
          tabs: const [
            Tab(text: 'الاختبارات المتاحة'),
            Tab(text: 'نتائجي'),
          ],
        ),
      ),
      body: Consumer<SubscriptionService>(
        builder: (context, subscriptionService, child) {
          return Column(
            children: [
              // فلتر المواد
              _buildSubjectFilter(subscriptionService.subscribedSubjects),

              // محتوى التبويبات
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [_buildAvailableExamsTab(), _buildMyResultsTab()],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSubjectFilter(List<Subject> subjects) {
    if (subjects.isEmpty) {
      return Container(
        padding: EdgeInsets.all(16.w),
        child: Card(
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: AppTheme.warningColor),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    'يجب تفعيل اشتراك في المواد أولاً لعرض الاختبارات',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.warningColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildFilterChip('جميع المواد', null),
            SizedBox(width: 8.w),
            ...subjects.map(
              (subject) => Padding(
                padding: EdgeInsets.only(left: 8.w),
                child: _buildFilterChip(subject.name, subject.id),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String? subjectId) {
    final isSelected = _selectedSubjectId == subjectId;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedSubjectId = selected ? subjectId : null;
        });
      },
      selectedColor: AppTheme.primaryColor.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.primaryColor,
      labelStyle: TextStyle(
        color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondaryColor,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
    );
  }

  Widget _buildAvailableExamsTab() {
    return Consumer<SubscriptionService>(
      builder: (context, subscriptionService, child) {
        final subscribedSubjectIds = subscriptionService.subscribedSubjects
            .map((subject) => subject.id)
            .toList();

        if (subscribedSubjectIds.isEmpty) {
          return _buildEmptyState(
            'لا توجد مواد مفعلة',
            'قم بتفعيل اشتراك في المواد لعرض الاختبارات',
            Icons.school_outlined,
          );
        }

        return StreamBuilder<List<Exam>>(
          stream: _getFilteredExamsStream(subscribedSubjectIds),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return _buildErrorState(snapshot.error.toString());
            }

            final exams = snapshot.data ?? [];

            if (exams.isEmpty) {
              return _buildEmptyState(
                'لا توجد اختبارات متاحة',
                'لم يتم إنشاء اختبارات للمواد المفعلة بعد',
                Icons.quiz_outlined,
              );
            }

            return ListView.builder(
              padding: EdgeInsets.all(16.w),
              itemCount: exams.length,
              itemBuilder: (context, index) {
                final exam = exams[index];
                final subject = subscriptionService.subscribedSubjects
                    .firstWhere((s) => s.id == exam.subjectId);

                return Padding(
                  padding: EdgeInsets.only(bottom: 16.h),
                  child: ExamCardWidget(
                    exam: exam,
                    subject: subject,
                    onTakeExam: () => _takeExam(exam),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  Widget _buildMyResultsTab() {
    // TODO: Implement results tab
    return _buildEmptyState(
      'قريباً',
      'سيتم إضافة صفحة النتائج قريباً',
      Icons.analytics_outlined,
    );
  }

  Widget _buildEmptyState(String title, String message, IconData icon) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64.sp,
              color: AppTheme.textSecondaryColor.withValues(alpha: 0.5),
            ),
            SizedBox(height: 16.h),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: AppTheme.textSecondaryColor,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64.sp, color: AppTheme.errorColor),
            SizedBox(height: 16.h),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: AppTheme.errorColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Stream<List<Exam>> _getFilteredExamsStream(
    List<String> subscribedSubjectIds,
  ) {
    if (_selectedSubjectId != null) {
      return ExamService.instance.getAvailableExams(_selectedSubjectId!);
    } else {
      // Get exams for all subscribed subjects
      return Stream.fromFuture(
        Future.wait(
          subscribedSubjectIds.map(
            (subjectId) =>
                ExamService.instance.getAvailableExams(subjectId).first,
          ),
        ).then((examLists) {
          final allExams = <Exam>[];
          for (final examList in examLists) {
            allExams.addAll(examList);
          }
          // Sort by creation date
          allExams.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          return allExams;
        }),
      );
    }
  }

  void _takeExam(Exam exam) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => ExamTakingPage(exam: exam)),
    );
  }
}
