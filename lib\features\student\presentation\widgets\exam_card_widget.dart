import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/exam_model.dart';
import '../../../../shared/models/subject_model.dart';

class ExamCardWidget extends StatelessWidget {
  final Exam exam;
  final Subject subject;
  final VoidCallback onTakeExam;

  const ExamCardWidget({
    super.key,
    required this.exam,
    required this.subject,
    required this.onTakeExam,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shadowColor: _getSubjectColor().withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [Colors.white, _getSubjectColor().withValues(alpha: 0.02)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(20.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    width: 50.w,
                    height: 50.h,
                    decoration: BoxDecoration(
                      color: _getSubjectColor().withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Icon(
                      _getSubjectIcon(),
                      color: _getSubjectColor(),
                      size: 24.sp,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          exam.title,
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppTheme.textPrimaryColor,
                              ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          subject.name,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                color: _getSubjectColor(),
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusBadge(),
                ],
              ),

              SizedBox(height: 16.h),

              // Description
              if (exam.description.isNotEmpty) ...[
                Text(
                  exam.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                SizedBox(height: 16.h),
              ],

              // Stats Row
              Row(
                children: [
                  _buildStatItem(
                    Icons.quiz_outlined,
                    '${exam.questionCount} سؤال',
                    AppTheme.primaryColor,
                  ),
                  SizedBox(width: 16.w),
                  _buildStatItem(
                    Icons.schedule_outlined,
                    exam.durationDisplayText,
                    AppTheme.secondaryColor,
                  ),
                  SizedBox(width: 16.w),
                  _buildStatItem(
                    Icons.star_outline,
                    '${exam.totalPoints} نقطة',
                    AppTheme.warningColor,
                  ),
                ],
              ),

              SizedBox(height: 16.h),

              // Passing Score
              Row(
                children: [
                  Icon(
                    Icons.trending_up,
                    size: 16.sp,
                    color: AppTheme.successColor,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    'درجة النجاح: ${exam.passingScore.toInt()}%',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.successColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 20.h),

              // Action Button
              SizedBox(
                width: double.infinity,
                height: 48.h,
                child: ElevatedButton(
                  onPressed: exam.isActive ? onTakeExam : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: exam.isActive
                        ? _getSubjectColor()
                        : AppTheme.textSecondaryColor,
                    foregroundColor: Colors.white,
                    elevation: exam.isActive ? 4 : 0,
                    shadowColor: exam.isActive
                        ? _getSubjectColor().withValues(alpha: 0.3)
                        : null,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        exam.isActive ? Icons.play_arrow : Icons.lock,
                        size: 20.sp,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        exam.isActive ? 'بدء الاختبار' : 'غير متاح',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Additional Info
              if (exam.startTime != null || exam.endTime != null) ...[
                SizedBox(height: 12.h),
                _buildTimeInfo(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge() {
    Color color;
    String text;
    IconData icon;

    switch (exam.status) {
      case ExamStatus.active:
        color = AppTheme.successColor;
        text = 'نشط';
        icon = Icons.play_circle;
        break;
      case ExamStatus.published:
        color = AppTheme.primaryColor;
        text = 'منشور';
        icon = Icons.public;
        break;
      case ExamStatus.completed:
        color = AppTheme.textSecondaryColor;
        text = 'مكتمل';
        icon = Icons.check_circle;
        break;
      default:
        color = AppTheme.warningColor;
        text = 'مسودة';
        icon = Icons.edit;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14.sp, color: color),
          SizedBox(width: 4.w),
          Text(
            text,
            style: TextStyle(
              fontSize: 11.sp,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String text, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16.sp, color: color),
        SizedBox(width: 4.w),
        Text(
          text,
          style: TextStyle(
            fontSize: 12.sp,
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildTimeInfo() {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppTheme.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (exam.startTime != null) ...[
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 14.sp,
                  color: AppTheme.textSecondaryColor,
                ),
                SizedBox(width: 4.w),
                Text(
                  'يبدأ: ${_formatDateTime(exam.startTime!)}',
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ],
          if (exam.endTime != null) ...[
            if (exam.startTime != null) SizedBox(height: 4.h),
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 14.sp,
                  color: AppTheme.textSecondaryColor,
                ),
                SizedBox(width: 4.w),
                Text(
                  'ينتهي: ${_formatDateTime(exam.endTime!)}',
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Color _getSubjectColor() {
    if (subject.color.isNotEmpty) {
      try {
        return Color(int.parse(subject.color.replaceFirst('#', '0xFF')));
      } catch (e) {
        return AppTheme.primaryColor;
      }
    }
    return AppTheme.primaryColor;
  }

  IconData _getSubjectIcon() {
    final name = subject.name.toLowerCase();
    if (name.contains('رياضيات') || name.contains('math'))
      return Icons.calculate;
    if (name.contains('فيزياء') || name.contains('physics'))
      return Icons.science;
    if (name.contains('كيمياء') || name.contains('chemistry'))
      return Icons.biotech;
    if (name.contains('أحياء') || name.contains('biology')) return Icons.eco;
    if (name.contains('عربية') || name.contains('arabic'))
      return Icons.language;
    if (name.contains('إنجليزية') || name.contains('english'))
      return Icons.translate;
    return Icons.quiz;
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
