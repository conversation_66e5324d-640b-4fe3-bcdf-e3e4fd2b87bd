import 'package:cloud_firestore/cloud_firestore.dart';
import '../../shared/models/subject_model.dart';
import '../../shared/models/subscription_code_model.dart';

class SampleData {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// إضافة المواد التجريبية
  static Future<void> addSampleSubjects() async {
    try {
      final subjects = [
        Subject(
          id: 'math',
          name: 'الرياضيات',
          description: 'مادة الرياضيات للصف الثالث الثانوي',
          iconUrl: '',
          color: '#6C5CE7',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Subject(
          id: 'physics',
          name: 'الفيزياء',
          description: 'مادة الفيزياء للصف الثالث الثانوي',
          iconUrl: '',
          color: '#00CEC9',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Subject(
          id: 'chemistry',
          name: 'الكيمياء',
          description: 'مادة الكيمياء للصف الثالث الثانوي',
          iconUrl: '',
          color: '#FF7675',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Subject(
          id: 'biology',
          name: 'الأحياء',
          description: 'مادة الأحياء للصف الثالث الثانوي',
          iconUrl: '',
          color: '#00B894',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Subject(
          id: 'arabic',
          name: 'اللغة العربية',
          description: 'مادة اللغة العربية للصف الثالث الثانوي',
          iconUrl: '',
          color: '#FFBE0B',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Subject(
          id: 'english',
          name: 'اللغة الإنجليزية',
          description: 'مادة اللغة الإنجليزية للصف الثالث الثانوي',
          iconUrl: '',
          color: '#E17055',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      for (final subject in subjects) {
        await _firestore
            .collection('subjects')
            .doc(subject.id)
            .set(subject.toMap());
        print('تم إضافة مادة: ${subject.name}');
      }

      print('✅ تم إضافة جميع المواد بنجاح');
    } catch (e) {
      print('❌ خطأ في إضافة المواد: $e');
    }
  }

  /// إضافة أكواد الاشتراك التجريبية
  static Future<void> addSampleCodes() async {
    try {
      final codes = [
        SubscriptionCode(
          id: 'code1',
          code: 'MATH2024',
          subjectIds: ['math'],
          status: CodeStatus.active,
          createdAt: DateTime.now(),
          expiresAt: DateTime.now().add(const Duration(days: 30)),
          createdByAdminId: 'admin1',
          notes: 'كود تجريبي لمادة الرياضيات',
        ),
        SubscriptionCode(
          id: 'code2',
          code: 'PHYSICS2024',
          subjectIds: ['physics'],
          status: CodeStatus.active,
          createdAt: DateTime.now(),
          expiresAt: DateTime.now().add(const Duration(days: 30)),
          createdByAdminId: 'admin1',
          notes: 'كود تجريبي لمادة الفيزياء',
        ),
        SubscriptionCode(
          id: 'code3',
          code: 'SCIENCE2024',
          subjectIds: ['physics', 'chemistry', 'biology'],
          status: CodeStatus.active,
          createdAt: DateTime.now(),
          expiresAt: DateTime.now().add(const Duration(days: 30)),
          createdByAdminId: 'admin1',
          notes: 'كود تجريبي للمواد العلمية',
        ),
        SubscriptionCode(
          id: 'code4',
          code: 'ALLIN2024',
          subjectIds: ['math', 'physics', 'chemistry', 'biology', 'arabic', 'english'],
          status: CodeStatus.active,
          createdAt: DateTime.now(),
          expiresAt: DateTime.now().add(const Duration(days: 30)),
          createdByAdminId: 'admin1',
          notes: 'كود تجريبي لجميع المواد',
        ),
        SubscriptionCode(
          id: 'code5',
          code: 'LANG2024',
          subjectIds: ['arabic', 'english'],
          status: CodeStatus.active,
          createdAt: DateTime.now(),
          expiresAt: DateTime.now().add(const Duration(days: 30)),
          createdByAdminId: 'admin1',
          notes: 'كود تجريبي للغات',
        ),
      ];

      for (final code in codes) {
        await _firestore
            .collection('subscription_codes')
            .doc(code.id)
            .set(code.toMap());
        print('تم إضافة كود: ${code.code} للمواد: ${code.subjectIds.join(', ')}');
      }

      print('✅ تم إضافة جميع الأكواد بنجاح');
    } catch (e) {
      print('❌ خطأ في إضافة الأكواد: $e');
    }
  }

  /// إضافة جميع البيانات التجريبية
  static Future<void> addAllSampleData() async {
    print('🚀 بدء إضافة البيانات التجريبية...');
    
    await addSampleSubjects();
    await Future.delayed(const Duration(seconds: 2));
    
    await addSampleCodes();
    
    print('🎉 تم إضافة جميع البيانات التجريبية بنجاح!');
  }

  /// حذف جميع البيانات التجريبية
  static Future<void> clearAllData() async {
    try {
      print('🗑️ بدء حذف البيانات...');

      // حذف المواد
      final subjectsSnapshot = await _firestore.collection('subjects').get();
      for (final doc in subjectsSnapshot.docs) {
        await doc.reference.delete();
      }

      // حذف الأكواد
      final codesSnapshot = await _firestore.collection('subscription_codes').get();
      for (final doc in codesSnapshot.docs) {
        await doc.reference.delete();
      }

      // حذف الاشتراكات
      final subscriptionsSnapshot = await _firestore.collection('user_subscriptions').get();
      for (final doc in subscriptionsSnapshot.docs) {
        await doc.reference.delete();
      }

      print('✅ تم حذف جميع البيانات بنجاح');
    } catch (e) {
      print('❌ خطأ في حذف البيانات: $e');
    }
  }

  /// عرض إحصائيات البيانات
  static Future<void> showDataStats() async {
    try {
      final subjectsCount = (await _firestore.collection('subjects').get()).docs.length;
      final codesCount = (await _firestore.collection('subscription_codes').get()).docs.length;
      final subscriptionsCount = (await _firestore.collection('user_subscriptions').get()).docs.length;

      print('📊 إحصائيات البيانات:');
      print('   المواد: $subjectsCount');
      print('   الأكواد: $codesCount');
      print('   الاشتراكات: $subscriptionsCount');
    } catch (e) {
      print('❌ خطأ في عرض الإحصائيات: $e');
    }
  }
}
