import 'package:flutter/foundation.dart';
import '../models/subject_model.dart';
import '../models/unit_model.dart';
import '../models/lesson_model.dart';
import '../models/question_model.dart';
import 'content_service.dart';
import 'exam_service.dart';
import 'offline_storage_service.dart';

/// خدمة تحميل المحتوى للعمل بدون إنترنت
class ContentDownloadService extends ChangeNotifier {
  static final ContentDownloadService _instance = ContentDownloadService._internal();
  static ContentDownloadService get instance => _instance;
  ContentDownloadService._internal();

  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  String _currentDownloadItem = '';
  String? _downloadError;

  bool get isDownloading => _isDownloading;
  double get downloadProgress => _downloadProgress;
  String get currentDownloadItem => _currentDownloadItem;
  String? get downloadError => _downloadError;

  /// تحميل محتوى مادة كاملة
  Future<bool> downloadSubjectContent(String subjectId) async {
    try {
      _setDownloading(true);
      _setProgress(0.0, 'بدء التحميل...');
      _clearError();

      // 1. تحميل معلومات المادة
      _setProgress(0.1, 'تحميل معلومات المادة...');
      final subject = await ContentService.instance.getSubjectById(subjectId);
      if (subject == null) {
        throw Exception('لم يتم العثور على المادة');
      }

      // 2. تحميل الوحدات
      _setProgress(0.2, 'تحميل الوحدات...');
      final units = await ContentService.instance.getSubjectUnits(subjectId);

      // 3. تحميل الدروس
      _setProgress(0.4, 'تحميل الدروس...');
      List<Lesson> allLessons = [];
      for (int i = 0; i < units.length; i++) {
        final lessons = await ContentService.instance.getUnitLessons(units[i].id);
        allLessons.addAll(lessons);
        _setProgress(0.4 + (0.2 * (i + 1) / units.length), 'تحميل دروس ${units[i].name}...');
      }

      // 4. تحميل الأسئلة العادية
      _setProgress(0.6, 'تحميل الأسئلة العادية...');
      List<Question> allQuestions = [];
      
      // أسئلة الوحدات
      for (int i = 0; i < units.length; i++) {
        final unitQuestions = await ExamService.instance.getQuestionsByUnit(units[i].id, false);
        allQuestions.addAll(unitQuestions);
        _setProgress(0.6 + (0.1 * (i + 1) / units.length), 'تحميل أسئلة ${units[i].name}...');
      }

      // أسئلة الدروس
      _setProgress(0.7, 'تحميل أسئلة الدروس...');
      for (int i = 0; i < allLessons.length; i++) {
        final lessonQuestions = await ExamService.instance.getQuestionsByLesson(allLessons[i].id, false);
        allQuestions.addAll(lessonQuestions);
        _setProgress(0.7 + (0.1 * (i + 1) / allLessons.length), 'تحميل أسئلة ${allLessons[i].name}...');
      }

      // 5. تحميل أسئلة الدورات
      _setProgress(0.8, 'تحميل أسئلة الدورات...');
      for (int i = 0; i < units.length; i++) {
        final courseQuestions = await ExamService.instance.getQuestionsByUnit(units[i].id, true);
        allQuestions.addAll(courseQuestions);
        _setProgress(0.8 + (0.1 * (i + 1) / units.length), 'تحميل دورات ${units[i].name}...');
      }

      // 6. حفظ البيانات محلياً
      _setProgress(0.9, 'حفظ البيانات محلياً...');
      
      // حفظ المادة
      final existingSubjects = await OfflineStorageService.instance.getOfflineSubjects();
      final updatedSubjects = existingSubjects.where((s) => s.id != subjectId).toList();
      updatedSubjects.add(subject);
      await OfflineStorageService.instance.saveSubjects(updatedSubjects);

      // حفظ الوحدات
      final existingUnits = await OfflineStorageService.instance.getOfflineUnits();
      final updatedUnits = existingUnits.where((u) => u.subjectId != subjectId).toList();
      updatedUnits.addAll(units);
      await OfflineStorageService.instance.saveUnits(updatedUnits);

      // حفظ الدروس
      final unitIds = units.map((u) => u.id).toList();
      final existingLessons = await OfflineStorageService.instance.getOfflineLessons();
      final updatedLessons = existingLessons.where((l) => !unitIds.contains(l.unitId)).toList();
      updatedLessons.addAll(allLessons);
      await OfflineStorageService.instance.saveLessons(updatedLessons);

      // حفظ الأسئلة
      final existingQuestions = await OfflineStorageService.instance.getOfflineQuestions();
      final updatedQuestions = existingQuestions.where((q) => q.subjectId != subjectId).toList();
      updatedQuestions.addAll(allQuestions);
      await OfflineStorageService.instance.saveQuestions(updatedQuestions);

      // إضافة المادة لقائمة المواد المحملة
      await OfflineStorageService.instance.addDownloadedSubject(subjectId);
      await OfflineStorageService.instance.updateLastSyncTime();

      _setProgress(1.0, 'تم التحميل بنجاح!');
      
      // إخفاء شريط التقدم بعد ثانيتين
      await Future.delayed(const Duration(seconds: 2));
      _setDownloading(false);
      
      return true;
    } catch (e) {
      _setError('خطأ في التحميل: $e');
      _setDownloading(false);
      return false;
    }
  }

  /// حذف محتوى مادة من التخزين المحلي
  Future<bool> deleteSubjectContent(String subjectId) async {
    try {
      await OfflineStorageService.instance.clearSubjectData(subjectId);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطأ في حذف المحتوى: $e');
      return false;
    }
  }

  /// تحديث محتوى مادة
  Future<bool> updateSubjectContent(String subjectId) async {
    return await downloadSubjectContent(subjectId);
  }

  /// فحص ما إذا كانت المادة محملة
  Future<bool> isSubjectDownloaded(String subjectId) async {
    return await OfflineStorageService.instance.isSubjectDownloaded(subjectId);
  }

  /// الحصول على قائمة المواد المحملة
  Future<List<String>> getDownloadedSubjects() async {
    return await OfflineStorageService.instance.getDownloadedSubjects();
  }

  /// الحصول على إحصائيات التخزين
  Future<Map<String, dynamic>> getStorageStats() async {
    return await OfflineStorageService.instance.getOfflineStats();
  }

  /// مسح جميع البيانات المحملة
  Future<bool> clearAllDownloadedContent() async {
    try {
      await OfflineStorageService.instance.clearAllOfflineData();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطأ في مسح البيانات: $e');
      return false;
    }
  }

  /// تحميل جميع المواد المتاحة
  Future<bool> downloadAllSubjects() async {
    try {
      _setDownloading(true);
      _setProgress(0.0, 'تحميل قائمة المواد...');
      
      final subjects = await ContentService.instance.getAllSubjects();
      if (subjects.isEmpty) {
        throw Exception('لا توجد مواد متاحة للتحميل');
      }

      for (int i = 0; i < subjects.length; i++) {
        final subject = subjects[i];
        final baseProgress = i / subjects.length;
        final nextProgress = (i + 1) / subjects.length;
        
        _setProgress(baseProgress, 'تحميل ${subject.name}...');
        
        // تحميل محتوى المادة مع تحديث التقدم
        await _downloadSubjectContentWithProgress(
          subject.id, 
          baseProgress, 
          nextProgress,
        );
      }

      _setProgress(1.0, 'تم تحميل جميع المواد بنجاح!');
      await Future.delayed(const Duration(seconds: 2));
      _setDownloading(false);
      
      return true;
    } catch (e) {
      _setError('خطأ في تحميل جميع المواد: $e');
      _setDownloading(false);
      return false;
    }
  }

  /// تحميل محتوى مادة مع تحديث التقدم المخصص
  Future<void> _downloadSubjectContentWithProgress(
    String subjectId, 
    double startProgress, 
    double endProgress,
  ) async {
    final subject = await ContentService.instance.getSubjectById(subjectId);
    if (subject == null) return;

    final progressRange = endProgress - startProgress;
    
    // تحميل الوحدات
    _setProgress(startProgress + (progressRange * 0.2), 'تحميل وحدات ${subject.name}...');
    final units = await ContentService.instance.getSubjectUnits(subjectId);

    // تحميل الدروس والأسئلة
    List<Lesson> allLessons = [];
    List<Question> allQuestions = [];
    
    for (int i = 0; i < units.length; i++) {
      final unitProgress = startProgress + (progressRange * (0.2 + (0.6 * (i + 1) / units.length)));
      _setProgress(unitProgress, 'تحميل محتوى ${units[i].name}...');
      
      final lessons = await ContentService.instance.getUnitLessons(units[i].id);
      allLessons.addAll(lessons);
      
      final unitQuestions = await ExamService.instance.getQuestionsByUnit(units[i].id, false);
      final courseQuestions = await ExamService.instance.getQuestionsByUnit(units[i].id, true);
      allQuestions.addAll(unitQuestions);
      allQuestions.addAll(courseQuestions);
      
      for (final lesson in lessons) {
        final lessonQuestions = await ExamService.instance.getQuestionsByLesson(lesson.id, false);
        allQuestions.addAll(lessonQuestions);
      }
    }

    // حفظ البيانات
    _setProgress(startProgress + (progressRange * 0.9), 'حفظ بيانات ${subject.name}...');
    
    // حفظ المادة
    final existingSubjects = await OfflineStorageService.instance.getOfflineSubjects();
    final updatedSubjects = existingSubjects.where((s) => s.id != subjectId).toList();
    updatedSubjects.add(subject);
    await OfflineStorageService.instance.saveSubjects(updatedSubjects);

    // حفظ الوحدات والدروس والأسئلة
    await _saveContentData(subjectId, units, allLessons, allQuestions);
    await OfflineStorageService.instance.addDownloadedSubject(subjectId);
  }

  /// حفظ بيانات المحتوى
  Future<void> _saveContentData(
    String subjectId,
    List<Unit> units,
    List<Lesson> lessons,
    List<Question> questions,
  ) async {
    // حفظ الوحدات
    final existingUnits = await OfflineStorageService.instance.getOfflineUnits();
    final updatedUnits = existingUnits.where((u) => u.subjectId != subjectId).toList();
    updatedUnits.addAll(units);
    await OfflineStorageService.instance.saveUnits(updatedUnits);

    // حفظ الدروس
    final unitIds = units.map((u) => u.id).toList();
    final existingLessons = await OfflineStorageService.instance.getOfflineLessons();
    final updatedLessons = existingLessons.where((l) => !unitIds.contains(l.unitId)).toList();
    updatedLessons.addAll(lessons);
    await OfflineStorageService.instance.saveLessons(updatedLessons);

    // حفظ الأسئلة
    final existingQuestions = await OfflineStorageService.instance.getOfflineQuestions();
    final updatedQuestions = existingQuestions.where((q) => q.subjectId != subjectId).toList();
    updatedQuestions.addAll(questions);
    await OfflineStorageService.instance.saveQuestions(updatedQuestions);
  }

  void _setDownloading(bool downloading) {
    _isDownloading = downloading;
    notifyListeners();
  }

  void _setProgress(double progress, String item) {
    _downloadProgress = progress;
    _currentDownloadItem = item;
    notifyListeners();
  }

  void _setError(String error) {
    _downloadError = error;
    notifyListeners();
  }

  void _clearError() {
    _downloadError = null;
    notifyListeners();
  }
}
