import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import 'flavors.dart';
import 'core/theme/app_theme.dart';
import 'features/student/presentation/pages/student_home_page.dart';
import 'features/admin/presentation/pages/admin_home_page.dart';
import 'shared/services/subscription_service.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => SubscriptionService.instance),
          ],
          child: MaterialApp(
            title: F.title,
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            home: F.isStudent ? const StudentHomePage() : const AdminHomePage(),
          ),
        );
      },
    );
  }
}
