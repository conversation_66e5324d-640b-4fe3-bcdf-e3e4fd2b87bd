# smart_test

A new Flutter project.

## Getting Started

# Smart Test - تطبيق الأسئلة الذكي

تطبيق Flutter متقدم لإدارة الأسئلة التعليمية مع نظام flavors للطالب والأدمن.

## المميزات

### تطبيق الطالب
- واجهة عصرية وجذابة بألوان متنوعة
- نظام اشتراكات بالأكواد
- تصنيف الأسئلة (وحدات، دروس، مفضلة، خاطئة، دورات)
- نظام أسئلة مؤتمت مع خيارات متعددة
- حفظ محلي آمن للعمل بدون إنترنت
- إحصائيات مفصلة للأداء

### تطبيق الأدمن
- إدارة شاملة للأسئلة والمواد
- إدارة الاشتراكات والأكواد
- لوحة تحكم متقدمة
- إحصائيات وتقارير

## التقنيات المستخدمة

- **Flutter** - إطار العمل الأساسي
- **Firebase** - قاعدة البيانات والمصادقة
- **Flavors** - لإدارة تطبيقين منفصلين
- **Provider** - إدارة الحالة
- **SQLite** - التخزين المحلي الآمن
- **Material Design 3** - التصميم العصري

## كيفية التشغيل

### تشغيل تطبيق الطالب
```bash
flutter run --flavor student -t lib/main_student.dart
```

### تشغيل تطبيق الأدمن
```bash
flutter run --flavor admin -t lib/main_admin.dart
```

### من VS Code
استخدم configurations الجاهزة في `.vscode/launch.json`:
- Smart Test - Student
- Smart Test - Admin

## إعداد Firebase

1. إنشاء مشروع Firebase جديد
2. إضافة تطبيقين Android منفصلين:
   - `com.smarttest.student`
   - `com.smarttest.admin`
3. تحديث ملفات `firebase_options_*.dart`
4. إضافة ملفات `google-services.json` للـ flavors

## هيكل المشروع

```
lib/
├── core/                 # الملفات الأساسية
│   ├── theme/           # الثيمات والألوان
│   ├── constants/       # الثوابت
│   └── utils/           # الأدوات المساعدة
├── features/            # المميزات
│   ├── student/         # مميزات الطالب
│   └── admin/           # مميزات الأدمن
├── shared/              # المكونات المشتركة
├── flavors.dart         # إعدادات Flavors
├── app.dart            # التطبيق الرئيسي
├── main_student.dart   # نقطة دخول الطالب
└── main_admin.dart     # نقطة دخول الأدمن
```

## الخطوات التالية

1. ✅ إعداد المشروع مع Flavors
2. 🔄 إعداد Firebase وقاعدة البيانات
3. ⏳ تطوير واجهات الطالب
4. ⏳ تطوير نظام الأسئلة
5. ⏳ تطوير التخزين المحلي الآمن
6. ⏳ تطوير تطبيق الأدمن
7. ⏳ الاختبار والتحسين
