import 'package:cloud_firestore/cloud_firestore.dart';

enum QuestionType {
  multipleChoice,
  trueFalse,
  shortAnswer,
  essay,
  matching,
  fillInTheBlank,
}

enum DifficultyLevel { easy, medium, hard }

class Question {
  final String id;
  final String subjectId;
  final String unitId; // الوحدة
  final String lessonId; // الدرس
  final bool isCourseQuestion; // هل هو سؤال دورة
  final String questionText;
  final QuestionType type;
  final DifficultyLevel difficulty;
  final List<String> options; // للأسئلة متعددة الخيارات
  final List<String> correctAnswers; // الإجابات الصحيحة
  final String explanation; // شرح الإجابة
  final int points; // النقاط
  final String imageUrl; // صورة السؤال (اختياري)
  final Map<String, dynamic> metadata; // بيانات إضافية
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdByAdminId;
  final bool isActive;

  const Question({
    required this.id,
    required this.subjectId,
    required this.unitId,
    required this.lessonId,
    required this.isCourseQuestion,
    required this.questionText,
    required this.type,
    required this.difficulty,
    required this.options,
    required this.correctAnswers,
    required this.explanation,
    required this.points,
    required this.imageUrl,
    required this.metadata,
    required this.createdAt,
    required this.updatedAt,
    required this.createdByAdminId,
    required this.isActive,
  });

  factory Question.fromMap(Map<String, dynamic> map) {
    return Question(
      id: map['id'] ?? '',
      subjectId: map['subjectId'] ?? '',
      unitId: map['unitId'] ?? '',
      lessonId: map['lessonId'] ?? '',
      isCourseQuestion: map['isCourseQuestion'] ?? false,
      questionText: map['questionText'] ?? '',
      type: QuestionType.values.firstWhere(
        (e) => e.toString() == 'QuestionType.${map['type']}',
        orElse: () => QuestionType.multipleChoice,
      ),
      difficulty: DifficultyLevel.values.firstWhere(
        (e) => e.toString() == 'DifficultyLevel.${map['difficulty']}',
        orElse: () => DifficultyLevel.medium,
      ),
      options: List<String>.from(map['options'] ?? []),
      correctAnswers: List<String>.from(map['correctAnswers'] ?? []),
      explanation: map['explanation'] ?? '',
      points: map['points'] ?? 1,
      imageUrl: map['imageUrl'] ?? '',
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
      createdByAdminId: map['createdByAdminId'] ?? '',
      isActive: map['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'subjectId': subjectId,
      'unitId': unitId,
      'lessonId': lessonId,
      'isCourseQuestion': isCourseQuestion,
      'questionText': questionText,
      'type': type.toString().split('.').last,
      'difficulty': difficulty.toString().split('.').last,
      'options': options,
      'correctAnswers': correctAnswers,
      'explanation': explanation,
      'points': points,
      'imageUrl': imageUrl,
      'metadata': metadata,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdByAdminId': createdByAdminId,
      'isActive': isActive,
    };
  }

  Question copyWith({
    String? id,
    String? subjectId,
    String? unitId,
    String? lessonId,
    bool? isCourseQuestion,
    String? questionText,
    QuestionType? type,
    DifficultyLevel? difficulty,
    List<String>? options,
    List<String>? correctAnswers,
    String? explanation,
    int? points,
    String? imageUrl,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdByAdminId,
    bool? isActive,
  }) {
    return Question(
      id: id ?? this.id,
      subjectId: subjectId ?? this.subjectId,
      unitId: unitId ?? this.unitId,
      lessonId: lessonId ?? this.lessonId,
      isCourseQuestion: isCourseQuestion ?? this.isCourseQuestion,
      questionText: questionText ?? this.questionText,
      type: type ?? this.type,
      difficulty: difficulty ?? this.difficulty,
      options: options ?? this.options,
      correctAnswers: correctAnswers ?? this.correctAnswers,
      explanation: explanation ?? this.explanation,
      points: points ?? this.points,
      imageUrl: imageUrl ?? this.imageUrl,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdByAdminId: createdByAdminId ?? this.createdByAdminId,
      isActive: isActive ?? this.isActive,
    );
  }

  // Helper methods
  String get typeDisplayName {
    switch (type) {
      case QuestionType.multipleChoice:
        return 'اختيار من متعدد';
      case QuestionType.trueFalse:
        return 'صح أم خطأ';
      case QuestionType.shortAnswer:
        return 'إجابة قصيرة';
      case QuestionType.essay:
        return 'مقال';
      case QuestionType.matching:
        return 'مطابقة';
      case QuestionType.fillInTheBlank:
        return 'ملء الفراغات';
    }
  }

  String get difficultyDisplayName {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return 'سهل';
      case DifficultyLevel.medium:
        return 'متوسط';
      case DifficultyLevel.hard:
        return 'صعب';
    }
  }

  bool isCorrectAnswer(String answer) {
    return correctAnswers.contains(answer);
  }

  bool isCorrectAnswers(List<String> answers) {
    if (answers.length != correctAnswers.length) return false;
    for (String answer in answers) {
      if (!correctAnswers.contains(answer)) return false;
    }
    return true;
  }
}
