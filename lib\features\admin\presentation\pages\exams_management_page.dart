import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/exam_model.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/services/content_service.dart';
import '../../../../shared/services/exam_service.dart';

class ExamsManagementPage extends StatefulWidget {
  const ExamsManagementPage({super.key});

  @override
  State<ExamsManagementPage> createState() => _ExamsManagementPageState();
}

class _ExamsManagementPageState extends State<ExamsManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  List<Exam> _draftExams = [];
  List<Exam> _publishedExams = [];
  List<Exam> _archivedExams = [];
  List<Subject> _subjects = [];

  bool _isLoading = true;
  String? _selectedSubjectId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // تحميل المواد
      final subjects = await ContentService.instance.getAllSubjects();

      // تحميل الاختبارات
      final allExamsSnapshot = await FirebaseFirestore.instance
          .collection('exams')
          .orderBy('createdAt', descending: true)
          .get();

      final allExams = allExamsSnapshot.docs
          .map((doc) => Exam.fromMap(doc.data()))
          .toList();

      setState(() {
        _subjects = subjects;
        _draftExams = allExams
            .where((exam) => exam.status == ExamStatus.draft)
            .toList();
        _publishedExams = allExams
            .where((exam) => exam.status == ExamStatus.published)
            .toList();
        _archivedExams = allExams
            .where((exam) => exam.status == ExamStatus.archived)
            .toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل البيانات: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'إدارة الاختبارات',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.secondaryGradient),
        ),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showCreateExamDialog,
            icon: const Icon(Icons.add),
            tooltip: 'إنشاء اختبار جديد',
          ),
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
            tooltip: 'فلترة',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
          tabs: [
            Tab(
              icon: const Icon(Icons.edit),
              text: 'مسودات (${_draftExams.length})',
            ),
            Tab(
              icon: const Icon(Icons.publish),
              text: 'منشورة (${_publishedExams.length})',
            ),
            Tab(
              icon: const Icon(Icons.archive),
              text: 'مؤرشفة (${_archivedExams.length})',
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // شريط الفلترة
                _buildFilterBar(),

                // محتوى التبويبات
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildExamsTab(_draftExams, 'draft'),
                      _buildExamsTab(_publishedExams, 'published'),
                      _buildExamsTab(_archivedExams, 'archived'),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // فلتر المادة
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedSubjectId,
              decoration: const InputDecoration(
                labelText: 'المادة',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              items: [
                const DropdownMenuItem(value: null, child: Text('جميع المواد')),
                ..._subjects.map(
                  (subject) => DropdownMenuItem(
                    value: subject.id,
                    child: Text(subject.name),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedSubjectId = value;
                });
              },
            ),
          ),

          SizedBox(width: 12.w),

          // زر إعادة تعيين الفلاتر
          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _selectedSubjectId = null;
              });
            },
            icon: Icon(Icons.clear, size: 16.sp),
            label: Text('إعادة تعيين'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.textSecondaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExamsTab(List<Exam> exams, String type) {
    // تطبيق الفلاتر
    List<Exam> filteredExams = exams.where((exam) {
      if (_selectedSubjectId != null && exam.subjectId != _selectedSubjectId) {
        return false;
      }
      return true;
    }).toList();

    if (filteredExams.isEmpty) {
      return _buildEmptyState(type);
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: filteredExams.length,
        itemBuilder: (context, index) {
          final exam = filteredExams[index];
          return _buildExamCard(exam, type);
        },
      ),
    );
  }

  Widget _buildEmptyState(String type) {
    String title, subtitle;
    IconData icon;

    switch (type) {
      case 'draft':
        title = 'لا توجد مسودات';
        subtitle = 'اضغط على + لإنشاء اختبار جديد';
        icon = Icons.edit_outlined;
        break;
      case 'published':
        title = 'لا توجد اختبارات منشورة';
        subtitle = 'الاختبارات المنشورة ستظهر هنا';
        icon = Icons.publish_outlined;
        break;
      case 'archived':
        title = 'لا توجد اختبارات مؤرشفة';
        subtitle = 'الاختبارات المؤرشفة ستظهر هنا';
        icon = Icons.archive_outlined;
        break;
      default:
        title = 'لا توجد اختبارات';
        subtitle = '';
        icon = Icons.assignment_outlined;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 80.sp, color: AppTheme.textSecondaryColor),
          SizedBox(height: 16.h),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          if (subtitle.isNotEmpty) ...[
            SizedBox(height: 8.h),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildExamCard(Exam exam, String type) {
    final subject = _subjects.firstWhere(
      (s) => s.id == exam.subjectId,
      orElse: () => Subject(
        id: '',
        name: 'غير محدد',
        description: '',
        iconUrl: '',
        color: '#6C5CE7',
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (type) {
      case 'draft':
        statusColor = AppTheme.textSecondaryColor;
        statusText = 'مسودة';
        statusIcon = Icons.edit;
        break;
      case 'published':
        statusColor = AppTheme.successColor;
        statusText = 'منشور';
        statusIcon = Icons.publish;
        break;
      case 'archived':
        statusColor = AppTheme.warningColor;
        statusText = 'مؤرشف';
        statusIcon = Icons.archive;
        break;
      default:
        statusColor = AppTheme.textSecondaryColor;
        statusText = 'غير محدد';
        statusIcon = Icons.help;
    }

    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              statusColor.withValues(alpha: 0.1),
              statusColor.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس الاختبار
              Row(
                children: [
                  // حالة الاختبار
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(statusIcon, size: 12.sp, color: Colors.white),
                        SizedBox(width: 4.w),
                        Text(
                          statusText,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(width: 8.w),

                  // نوع الاختبار
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      exam.statusDisplayName,
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                  const Spacer(),

                  // أزرار الإجراءات
                  PopupMenuButton<String>(
                    onSelected: (action) => _handleExamAction(action, exam),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'duplicate',
                        child: Row(
                          children: [
                            Icon(Icons.copy),
                            SizedBox(width: 8),
                            Text('نسخ'),
                          ],
                        ),
                      ),
                      if (type == 'draft') ...[
                        const PopupMenuItem(
                          value: 'publish',
                          child: Row(
                            children: [
                              Icon(Icons.publish),
                              SizedBox(width: 8),
                              Text('نشر'),
                            ],
                          ),
                        ),
                      ],
                      if (type == 'published') ...[
                        const PopupMenuItem(
                          value: 'archive',
                          child: Row(
                            children: [
                              Icon(Icons.archive),
                              SizedBox(width: 8),
                              Text('أرشفة'),
                            ],
                          ),
                        ),
                      ],
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // عنوان الاختبار
              Text(
                exam.title,
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              if (exam.description.isNotEmpty) ...[
                SizedBox(height: 8.h),
                Text(
                  exam.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              SizedBox(height: 12.h),

              // معلومات الاختبار
              Row(
                children: [
                  Icon(
                    Icons.book,
                    size: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    subject.name,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Icon(
                    Icons.quiz,
                    size: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    '${exam.questionIds.length} سؤال',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Icon(
                    Icons.timer,
                    size: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    '${exam.duration} دقيقة',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleExamAction(String action, Exam exam) {
    switch (action) {
      case 'edit':
        _editExam(exam);
        break;
      case 'duplicate':
        _duplicateExam(exam);
        break;
      case 'publish':
        _publishExam(exam);
        break;
      case 'archive':
        _archiveExam(exam);
        break;
      case 'delete':
        _deleteExam(exam);
        break;
    }
  }

  void _editExam(Exam exam) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل الاختبار - قيد التطوير')),
    );
  }

  void _duplicateExam(Exam exam) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('نسخ الاختبار - قيد التطوير')));
  }

  void _publishExam(Exam exam) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('نشر الاختبار - قيد التطوير')));
  }

  void _archiveExam(Exam exam) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('أرشفة الاختبار - قيد التطوير')),
    );
  }

  void _deleteExam(Exam exam) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا الاختبار؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ExamService.instance.deleteExam(exam.id);
        _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الاختبار بنجاح'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('خطأ في الحذف: $e')));
        }
      }
    }
  }

  void _showCreateExamDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إنشاء اختبار جديد - قيد التطوير')),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة الاختبارات'),
        content: const Text(
          'استخدم الفلاتر في الأعلى لتصفية الاختبارات حسب المادة',
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
