import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import 'questions_viewer_page.dart';
import 'subject_units_page.dart';

class WrongQuestionsPage extends StatefulWidget {
  final Subject subject;

  const WrongQuestionsPage({
    super.key,
    required this.subject,
  });

  @override
  State<WrongQuestionsPage> createState() => _WrongQuestionsPageState();
}

class _WrongQuestionsPageState extends State<WrongQuestionsPage> {
  int _totalWrongQuestions = 0;
  int _wrongQuestionsByUnits = 0;
  int _wrongQuestionsByLessons = 0;

  @override
  void initState() {
    super.initState();
    _loadWrongQuestionsCounts();
  }

  Future<void> _loadWrongQuestionsCounts() async {
    // هنا سنحتاج دوال لحساب عدد الأسئلة الخاطئة
    // مؤقتاً سنضع قيم تجريبية
    setState(() {
      _totalWrongQuestions = 0;
      _wrongQuestionsByUnits = 0;
      _wrongQuestionsByLessons = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _loadWrongQuestionsCounts,
      child: ListView(
        padding: EdgeInsets.all(16.w),
        children: [
          // رسالة توضيحية
          _buildInfoCard(),
          
          SizedBox(height: 16.h),
          
          // جميع الأسئلة الخاطئة
          _buildOptionCard(
            title: 'جميع الأسئلة الخاطئة',
            subtitle: 'جميع الأسئلة التي أجبت عليها خطأ',
            count: _totalWrongQuestions,
            icon: Icons.error,
            color: AppTheme.errorColor,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => QuestionsViewerPage(
                    title: 'الأسئلة الخاطئة',
                    subject: widget.subject,
                    questionType: QuestionFilterType.wrong,
                  ),
                ),
              );
            },
          ),
          
          SizedBox(height: 16.h),
          
          // الأسئلة الخاطئة حسب الوحدات
          _buildOptionCard(
            title: 'الخاطئة حسب الوحدات',
            subtitle: 'تصفح الأسئلة الخاطئة مقسمة حسب الوحدات',
            count: _wrongQuestionsByUnits,
            icon: Icons.folder_special,
            color: AppTheme.primaryColor,
            onTap: () {
              _showUnitsSelection();
            },
          ),
          
          SizedBox(height: 16.h),
          
          // الأسئلة الخاطئة حسب الدروس
          _buildOptionCard(
            title: 'الخاطئة حسب الدروس',
            subtitle: 'تصفح الأسئلة الخاطئة مقسمة حسب الدروس',
            count: _wrongQuestionsByLessons,
            icon: Icons.play_lesson,
            color: AppTheme.secondaryColor,
            onTap: () {
              _showLessonsSelection();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          gradient: LinearGradient(
            colors: [
              AppTheme.errorColor.withValues(alpha: 0.1),
              AppTheme.errorColor.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: AppTheme.errorColor,
              size: 24.sp,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                'الأسئلة التي تجيب عليها خطأ تُضاف هنا تلقائياً، وتختفي عند الإجابة الصحيحة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionCard({
    required String title,
    required String subtitle,
    required int count,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16.r),
        child: Container(
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            children: [
              // أيقونة
              Container(
                width: 60.w,
                height: 60.h,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 30.sp,
                ),
              ),
              
              SizedBox(width: 16.w),
              
              // المحتوى
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Icon(
                          Icons.quiz_outlined,
                          size: 16.sp,
                          color: color,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          '$count سؤال',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: color,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // سهم
              Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.textSecondaryColor,
                size: 16.sp,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showUnitsSelection() {
    // هنا سنعرض قائمة الوحدات للاختيار منها
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'اختر الوحدة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              'سيتم إضافة قائمة الوحدات هنا',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }

  void _showLessonsSelection() {
    // هنا سنعرض قائمة الدروس للاختيار منها
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'اختر الدرس',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              'سيتم إضافة قائمة الدروس هنا',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }
}
