import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/question_model.dart';
import '../models/exam_model.dart';
import '../models/exam_result_model.dart';

class ExamService extends ChangeNotifier {
  static final ExamService _instance = ExamService._internal();
  static ExamService get instance => _instance;
  ExamService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Questions Management
  Future<String> createQuestion(Question question) async {
    try {
      final docRef = _firestore.collection('questions').doc();
      final questionWithId = question.copyWith(id: docRef.id);

      await docRef.set(questionWithId.toMap());
      print('تم إنشاء السؤال: ${questionWithId.id}');

      notifyListeners();
      return questionWithId.id;
    } catch (e) {
      print('خطأ في إنشاء السؤال: $e');
      rethrow;
    }
  }

  Future<void> updateQuestion(Question question) async {
    try {
      await _firestore
          .collection('questions')
          .doc(question.id)
          .update(question.copyWith(updatedAt: DateTime.now()).toMap());

      print('تم تحديث السؤال: ${question.id}');
      notifyListeners();
    } catch (e) {
      print('خطأ في تحديث السؤال: $e');
      rethrow;
    }
  }

  Future<void> deleteQuestion(String questionId) async {
    try {
      await _firestore.collection('questions').doc(questionId).delete();
      print('تم حذف السؤال: $questionId');
      notifyListeners();
    } catch (e) {
      print('خطأ في حذف السؤال: $e');
      rethrow;
    }
  }

  Future<Question?> getQuestion(String questionId) async {
    try {
      final doc = await _firestore
          .collection('questions')
          .doc(questionId)
          .get();
      if (doc.exists) {
        return Question.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      print('خطأ في جلب السؤال: $e');
      return null;
    }
  }

  Stream<List<Question>> getQuestionsBySubject(String subjectId) {
    return _firestore
        .collection('questions')
        .where('subjectId', isEqualTo: subjectId)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Question.fromMap(doc.data())).toList(),
        );
  }

  Stream<List<Question>> getAllQuestions() {
    return _firestore
        .collection('questions')
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Question.fromMap(doc.data())).toList(),
        );
  }

  // Exams Management
  Future<String> createExam(Exam exam) async {
    try {
      final docRef = _firestore.collection('exams').doc();
      final examWithId = exam.copyWith(id: docRef.id);

      await docRef.set(examWithId.toMap());
      print('تم إنشاء الاختبار: ${examWithId.id}');

      notifyListeners();
      return examWithId.id;
    } catch (e) {
      print('خطأ في إنشاء الاختبار: $e');
      rethrow;
    }
  }

  Future<void> updateExam(Exam exam) async {
    try {
      await _firestore
          .collection('exams')
          .doc(exam.id)
          .update(exam.copyWith(updatedAt: DateTime.now()).toMap());

      print('تم تحديث الاختبار: ${exam.id}');
      notifyListeners();
    } catch (e) {
      print('خطأ في تحديث الاختبار: $e');
      rethrow;
    }
  }

  Future<void> deleteExam(String examId) async {
    try {
      await _firestore.collection('exams').doc(examId).delete();
      print('تم حذف الاختبار: $examId');
      notifyListeners();
    } catch (e) {
      print('خطأ في حذف الاختبار: $e');
      rethrow;
    }
  }

  Future<Exam?> getExam(String examId) async {
    try {
      final doc = await _firestore.collection('exams').doc(examId).get();
      if (doc.exists) {
        return Exam.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      print('خطأ في جلب الاختبار: $e');
      return null;
    }
  }

  Stream<List<Exam>> getExamsBySubject(String subjectId) {
    return _firestore
        .collection('exams')
        .where('subjectId', isEqualTo: subjectId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Exam.fromMap(doc.data())).toList(),
        );
  }

  Stream<List<Exam>> getAvailableExams(String subjectId) {
    return _firestore
        .collection('exams')
        .where('subjectId', isEqualTo: subjectId)
        .where('status', whereIn: ['published', 'active'])
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Exam.fromMap(doc.data())).toList(),
        );
  }

  // Exam Results Management
  Future<String> startExam(
    String examId,
    String userId,
    String deviceId,
  ) async {
    try {
      final docRef = _firestore.collection('exam_results').doc();

      final examResult = ExamResult(
        id: docRef.id,
        examId: examId,
        userId: userId,
        deviceId: deviceId,
        answers: [],
        totalPoints: 0,
        earnedPoints: 0,
        percentage: 0.0,
        isPassed: false,
        status: ResultStatus.inProgress,
        startedAt: DateTime.now(),
        timeSpent: 0,
        attemptNumber: await _getNextAttemptNumber(examId, userId),
        metadata: {},
      );

      await docRef.set(examResult.toMap());
      print('تم بدء الاختبار: ${examResult.id}');

      return examResult.id;
    } catch (e) {
      print('خطأ في بدء الاختبار: $e');
      rethrow;
    }
  }

  Future<void> submitExamResult(ExamResult result) async {
    try {
      await _firestore
          .collection('exam_results')
          .doc(result.id)
          .update(result.toMap());

      print('تم إرسال نتيجة الاختبار: ${result.id}');
      notifyListeners();
    } catch (e) {
      print('خطأ في إرسال نتيجة الاختبار: $e');
      rethrow;
    }
  }

  Future<ExamResult?> getExamResult(String resultId) async {
    try {
      final doc = await _firestore
          .collection('exam_results')
          .doc(resultId)
          .get();
      if (doc.exists) {
        return ExamResult.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      print('خطأ في جلب نتيجة الاختبار: $e');
      return null;
    }
  }

  Stream<List<ExamResult>> getUserExamResults(String userId) {
    return _firestore
        .collection('exam_results')
        .where('userId', isEqualTo: userId)
        .orderBy('startedAt', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => ExamResult.fromMap(doc.data()))
              .toList(),
        );
  }

  Future<int> _getNextAttemptNumber(String examId, String userId) async {
    try {
      final results = await _firestore
          .collection('exam_results')
          .where('examId', isEqualTo: examId)
          .where('userId', isEqualTo: userId)
          .get();

      return results.docs.length + 1;
    } catch (e) {
      print('خطأ في حساب رقم المحاولة: $e');
      return 1;
    }
  }

  // Helper methods
  Future<List<Question>> getExamQuestions(Exam exam) async {
    try {
      final questions = <Question>[];
      for (String questionId in exam.questionIds) {
        final question = await getQuestion(questionId);
        if (question != null) {
          questions.add(question);
        }
      }
      return questions;
    } catch (e) {
      print('خطأ في جلب أسئلة الاختبار: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>> getExamStatistics(String examId) async {
    try {
      final results = await _firestore
          .collection('exam_results')
          .where('examId', isEqualTo: examId)
          .where('status', isEqualTo: 'completed')
          .get();

      if (results.docs.isEmpty) {
        return {
          'totalAttempts': 0,
          'averageScore': 0.0,
          'passRate': 0.0,
          'highestScore': 0.0,
          'lowestScore': 0.0,
        };
      }

      final scores = results.docs
          .map((doc) => ExamResult.fromMap(doc.data()).percentage)
          .toList();

      final passedCount = results.docs
          .where((doc) => ExamResult.fromMap(doc.data()).isPassed)
          .length;

      return {
        'totalAttempts': results.docs.length,
        'averageScore': scores.reduce((a, b) => a + b) / scores.length,
        'passRate': (passedCount / results.docs.length) * 100,
        'highestScore': scores.reduce((a, b) => a > b ? a : b),
        'lowestScore': scores.reduce((a, b) => a < b ? a : b),
      };
    } catch (e) {
      print('خطأ في جلب إحصائيات الاختبار: $e');
      return {};
    }
  }

  /// الحصول على أسئلة الوحدة
  Future<List<Question>> getQuestionsByUnit(
    String unitId,
    bool isCourseQuestion,
  ) async {
    try {
      final querySnapshot = await _firestore
          .collection('questions')
          .where('unitId', isEqualTo: unitId)
          .where('isCourseQuestion', isEqualTo: isCourseQuestion)
          .where('isActive', isEqualTo: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Question.fromMap(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل أسئلة الوحدة: $e');
      return [];
    }
  }

  /// الحصول على أسئلة الدرس
  Future<List<Question>> getQuestionsByLesson(
    String lessonId,
    bool isCourseQuestion,
  ) async {
    try {
      final querySnapshot = await _firestore
          .collection('questions')
          .where('lessonId', isEqualTo: lessonId)
          .where('isCourseQuestion', isEqualTo: isCourseQuestion)
          .where('isActive', isEqualTo: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Question.fromMap(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل أسئلة الدرس: $e');
      return [];
    }
  }

  /// البحث في الأسئلة
  Future<List<Question>> searchQuestions(String query, String subjectId) async {
    try {
      final querySnapshot = await _firestore
          .collection('questions')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .get();

      final questions = querySnapshot.docs
          .map((doc) => Question.fromMap(doc.data()))
          .toList();

      // فلترة النتائج محلياً
      return questions.where((question) {
        return question.questionText.toLowerCase().contains(
              query.toLowerCase(),
            ) ||
            question.explanation.toLowerCase().contains(query.toLowerCase());
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الأسئلة: $e');
      return [];
    }
  }
}
