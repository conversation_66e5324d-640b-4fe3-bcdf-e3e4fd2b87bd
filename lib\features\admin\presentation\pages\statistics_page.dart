import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/theme/app_theme.dart';

class StatisticsPage extends StatefulWidget {
  const StatisticsPage({super.key});

  @override
  State<StatisticsPage> createState() => _StatisticsPageState();
}

class _StatisticsPageState extends State<StatisticsPage> {
  bool _isLoading = true;
  Map<String, dynamic> _stats = {};

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    try {
      setState(() => _isLoading = true);

      // تحميل إحصائيات المواد
      final subjectsSnapshot = await FirebaseFirestore.instance
          .collection('subjects')
          .get();

      // تحميل إحصائيات الأسئلة
      final questionsSnapshot = await FirebaseFirestore.instance
          .collection('questions')
          .get();

      // تحميل إحصائيات الاختبارات
      final examsSnapshot = await FirebaseFirestore.instance
          .collection('exams')
          .get();

      // تحميل إحصائيات الأكواد
      final codesSnapshot = await FirebaseFirestore.instance
          .collection('subscription_codes')
          .get();

      // تحميل إحصائيات النتائج
      final resultsSnapshot = await FirebaseFirestore.instance
          .collection('exam_results')
          .get();

      final now = DateTime.now();
      final activeCodes = codesSnapshot.docs.where((doc) {
        final data = doc.data();
        final status = data['status'] ?? 'active';
        final expiresAt = data['expiresAt'];
        return status == 'active' &&
            (expiresAt == null ||
                (expiresAt as Timestamp).toDate().isAfter(now));
      }).length;

      final usedCodes = codesSnapshot.docs.where((doc) {
        final data = doc.data();
        return data['status'] == 'used';
      }).length;

      final regularQuestions = questionsSnapshot.docs.where((doc) {
        final data = doc.data();
        return !(data['isCourseQuestion'] ?? false);
      }).length;

      final courseQuestions = questionsSnapshot.docs.where((doc) {
        final data = doc.data();
        return data['isCourseQuestion'] ?? false;
      }).length;

      final publishedExams = examsSnapshot.docs.where((doc) {
        final data = doc.data();
        return data['status'] == 'published';
      }).length;

      final completedResults = resultsSnapshot.docs.where((doc) {
        final data = doc.data();
        return data['status'] == 'completed';
      }).length;

      setState(() {
        _stats = {
          'subjects': subjectsSnapshot.docs.length,
          'totalQuestions': questionsSnapshot.docs.length,
          'regularQuestions': regularQuestions,
          'courseQuestions': courseQuestions,
          'totalExams': examsSnapshot.docs.length,
          'publishedExams': publishedExams,
          'totalCodes': codesSnapshot.docs.length,
          'activeCodes': activeCodes,
          'usedCodes': usedCodes,
          'totalResults': resultsSnapshot.docs.length,
          'completedResults': completedResults,
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الإحصائيات: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'الإحصائيات',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.successGradient),
        ),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _loadStatistics,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadStatistics,
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'نظرة عامة',
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryColor,
                          ),
                    ),
                    SizedBox(height: 16.h),

                    // إحصائيات المواد والأسئلة
                    _buildStatsSection('المحتوى التعليمي', [
                      _buildStatCard(
                        'المواد',
                        _stats['subjects']?.toString() ?? '0',
                        Icons.book,
                        AppTheme.primaryColor,
                      ),
                      _buildStatCard(
                        'إجمالي الأسئلة',
                        _stats['totalQuestions']?.toString() ?? '0',
                        Icons.quiz,
                        AppTheme.secondaryColor,
                      ),
                      _buildStatCard(
                        'أسئلة عادية',
                        _stats['regularQuestions']?.toString() ?? '0',
                        Icons.help_outline,
                        AppTheme.accentColor,
                      ),
                      _buildStatCard(
                        'أسئلة دورات',
                        _stats['courseQuestions']?.toString() ?? '0',
                        Icons.school,
                        AppTheme.warningColor,
                      ),
                    ]),

                    SizedBox(height: 24.h),

                    // إحصائيات الاختبارات
                    _buildStatsSection('الاختبارات', [
                      _buildStatCard(
                        'إجمالي الاختبارات',
                        _stats['totalExams']?.toString() ?? '0',
                        Icons.assignment,
                        AppTheme.primaryColor,
                      ),
                      _buildStatCard(
                        'اختبارات منشورة',
                        _stats['publishedExams']?.toString() ?? '0',
                        Icons.publish,
                        AppTheme.successColor,
                      ),
                      _buildStatCard(
                        'إجمالي النتائج',
                        _stats['totalResults']?.toString() ?? '0',
                        Icons.analytics,
                        AppTheme.secondaryColor,
                      ),
                      _buildStatCard(
                        'نتائج مكتملة',
                        _stats['completedResults']?.toString() ?? '0',
                        Icons.check_circle,
                        AppTheme.accentColor,
                      ),
                    ]),

                    SizedBox(height: 24.h),

                    // إحصائيات الأكواد
                    _buildStatsSection('أكواد التفعيل', [
                      _buildStatCard(
                        'إجمالي الأكواد',
                        _stats['totalCodes']?.toString() ?? '0',
                        Icons.vpn_key,
                        AppTheme.primaryColor,
                      ),
                      _buildStatCard(
                        'أكواد نشطة',
                        _stats['activeCodes']?.toString() ?? '0',
                        Icons.check_circle,
                        AppTheme.successColor,
                      ),
                      _buildStatCard(
                        'أكواد مستخدمة',
                        _stats['usedCodes']?.toString() ?? '0',
                        Icons.check_circle,
                        AppTheme.warningColor,
                      ),
                      _buildStatCard(
                        'معدل الاستخدام',
                        _calculateUsageRate(),
                        Icons.trending_up,
                        AppTheme.accentColor,
                      ),
                    ]),

                    SizedBox(height: 24.h),

                    // معلومات إضافية
                    _buildInfoSection(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildStatsSection(String title, List<Widget> cards) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        SizedBox(height: 12.h),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12.w,
          mainAxisSpacing: 12.h,
          childAspectRatio: 1.3,
          children: cards,
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 4,
      shadowColor: color.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 40.w,
                height: 40.h,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Icon(icon, color: Colors.white, size: 20.sp),
              ),
              SizedBox(height: 8.h),
              Text(
                value,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryColor,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  'معلومات النظام',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            _buildInfoRow('آخر تحديث', _formatDateTime(DateTime.now())),
            SizedBox(height: 8.h),
            _buildInfoRow('إصدار التطبيق', '1.0.0'),
            SizedBox(height: 8.h),
            _buildInfoRow('قاعدة البيانات', 'Firebase Firestore'),
            SizedBox(height: 8.h),
            _buildInfoRow('حالة النظام', 'يعمل بشكل طبيعي'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 14.sp, color: AppTheme.textSecondaryColor),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14.sp,
            color: AppTheme.textPrimaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  String _calculateUsageRate() {
    final totalCodes = _stats['totalCodes'] ?? 0;
    final usedCodes = _stats['usedCodes'] ?? 0;

    if (totalCodes == 0) return '0%';

    final rate = (usedCodes / totalCodes * 100).round();
    return '$rate%';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
