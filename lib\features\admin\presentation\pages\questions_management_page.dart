import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/question_model.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/unit_model.dart';
import '../../../../shared/services/content_service.dart';
import '../../../../shared/services/exam_service.dart';

class QuestionsManagementPage extends StatefulWidget {
  const QuestionsManagementPage({super.key});

  @override
  State<QuestionsManagementPage> createState() =>
      _QuestionsManagementPageState();
}

class _QuestionsManagementPageState extends State<QuestionsManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  List<Question> _regularQuestions = [];
  List<Question> _courseQuestions = [];
  List<Subject> _subjects = [];
  List<Unit> _units = [];
  List<Lesson> _lessons = [];

  bool _isLoading = true;
  String? _selectedSubjectId;
  String? _selectedUnitId;
  String? _selectedLessonId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // تحميل المواد
      final subjects = await ContentService.instance.getAllSubjects();

      // تحميل الأسئلة العادية والدورات
      final allQuestionsSnapshot = await FirebaseFirestore.instance
          .collection('questions')
          .where('isActive', isEqualTo: true)
          .get();

      final allQuestions = allQuestionsSnapshot.docs
          .map((doc) => Question.fromMap(doc.data()))
          .toList();

      final regularQuestions = allQuestions
          .where((q) => !q.isCourseQuestion)
          .toList();
      final courseQuestions = allQuestions
          .where((q) => q.isCourseQuestion)
          .toList();

      setState(() {
        _subjects = subjects;
        _regularQuestions = regularQuestions;
        _courseQuestions = courseQuestions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل البيانات: $e')));
      }
    }
  }

  Future<void> _loadUnitsAndLessons(String subjectId) async {
    try {
      final units = await ContentService.instance.getSubjectUnits(subjectId);
      setState(() {
        _units = units;
        _selectedUnitId = null;
        _selectedLessonId = null;
        _lessons.clear();
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الوحدات: $e')));
      }
    }
  }

  Future<void> _loadLessons(String unitId) async {
    try {
      final lessons = await ContentService.instance.getUnitLessons(unitId);
      setState(() {
        _lessons = lessons;
        _selectedLessonId = null;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الدروس: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'إدارة الأسئلة',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
        ),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showAddQuestionDialog,
            icon: const Icon(Icons.add),
            tooltip: 'إضافة سؤال جديد',
          ),
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
            tooltip: 'فلترة',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
          tabs: const [
            Tab(icon: Icon(Icons.quiz), text: 'الأسئلة العادية'),
            Tab(icon: Icon(Icons.school), text: 'أسئلة الدورات'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // شريط الفلترة
                _buildFilterBar(),

                // محتوى التبويبات
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildQuestionsTab(_regularQuestions, false),
                      _buildQuestionsTab(_courseQuestions, true),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // فلتر المادة
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedSubjectId,
                  decoration: const InputDecoration(
                    labelText: 'المادة',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('جميع المواد'),
                    ),
                    ..._subjects.map(
                      (subject) => DropdownMenuItem(
                        value: subject.id,
                        child: Text(subject.name),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedSubjectId = value;
                      _selectedUnitId = null;
                      _selectedLessonId = null;
                      _units.clear();
                      _lessons.clear();
                    });
                    if (value != null) {
                      _loadUnitsAndLessons(value);
                    }
                  },
                ),
              ),

              SizedBox(width: 12.w),

              // فلتر الوحدة
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedUnitId,
                  decoration: const InputDecoration(
                    labelText: 'الوحدة',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('جميع الوحدات'),
                    ),
                    ..._units.map(
                      (unit) => DropdownMenuItem(
                        value: unit.id,
                        child: Text(unit.name),
                      ),
                    ),
                  ],
                  onChanged: _selectedSubjectId == null
                      ? null
                      : (value) {
                          setState(() {
                            _selectedUnitId = value;
                            _selectedLessonId = null;
                            _lessons.clear();
                          });
                          if (value != null) {
                            _loadLessons(value);
                          }
                        },
                ),
              ),
            ],
          ),

          SizedBox(height: 12.h),

          Row(
            children: [
              // فلتر الدرس
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedLessonId,
                  decoration: const InputDecoration(
                    labelText: 'الدرس',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('جميع الدروس'),
                    ),
                    ..._lessons.map(
                      (lesson) => DropdownMenuItem(
                        value: lesson.id,
                        child: Text(lesson.name),
                      ),
                    ),
                  ],
                  onChanged: _selectedUnitId == null
                      ? null
                      : (value) {
                          setState(() {
                            _selectedLessonId = value;
                          });
                        },
                ),
              ),

              SizedBox(width: 12.w),

              // زر إعادة تعيين الفلاتر
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _selectedSubjectId = null;
                    _selectedUnitId = null;
                    _selectedLessonId = null;
                    _units.clear();
                    _lessons.clear();
                  });
                },
                icon: Icon(Icons.clear, size: 16.sp),
                label: Text('إعادة تعيين'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.textSecondaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionsTab(List<Question> questions, bool isCourse) {
    // تطبيق الفلاتر
    List<Question> filteredQuestions = questions.where((question) {
      if (_selectedSubjectId != null &&
          question.subjectId != _selectedSubjectId) {
        return false;
      }
      if (_selectedUnitId != null && question.unitId != _selectedUnitId) {
        return false;
      }
      if (_selectedLessonId != null && question.lessonId != _selectedLessonId) {
        return false;
      }
      return true;
    }).toList();

    if (filteredQuestions.isEmpty) {
      return _buildEmptyState(isCourse);
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: filteredQuestions.length,
        itemBuilder: (context, index) {
          final question = filteredQuestions[index];
          return _buildQuestionCard(question, isCourse);
        },
      ),
    );
  }

  Widget _buildEmptyState(bool isCourse) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isCourse ? Icons.school_outlined : Icons.quiz_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            isCourse ? 'لا توجد أسئلة دورات' : 'لا توجد أسئلة عادية',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'اضغط على + لإضافة سؤال جديد',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionCard(Question question, bool isCourse) {
    final subject = _subjects.firstWhere(
      (s) => s.id == question.subjectId,
      orElse: () => Subject(
        id: '',
        name: 'غير محدد',
        description: '',
        iconUrl: '',
        color: '#6C5CE7',
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              (isCourse ? AppTheme.accentColor : AppTheme.primaryColor)
                  .withValues(alpha: 0.1),
              (isCourse ? AppTheme.accentColor : AppTheme.primaryColor)
                  .withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس السؤال
              Row(
                children: [
                  // نوع السؤال
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: isCourse
                          ? AppTheme.accentColor
                          : AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      isCourse ? 'دورة' : 'عادي',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                  SizedBox(width: 8.w),

                  // نوع السؤال
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.secondaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      question.typeDisplayName,
                      style: TextStyle(
                        color: AppTheme.secondaryColor,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                  SizedBox(width: 8.w),

                  // مستوى الصعوبة
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: _getDifficultyColor(
                        question.difficulty,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      question.difficultyDisplayName,
                      style: TextStyle(
                        color: _getDifficultyColor(question.difficulty),
                        fontSize: 10.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                  const Spacer(),

                  // النقاط
                  Text(
                    '${question.points} نقطة',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppTheme.textSecondaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // نص السؤال
              Text(
                question.questionText,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(height: 8.h),

              // معلومات إضافية
              Row(
                children: [
                  Icon(
                    Icons.book,
                    size: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    subject.name,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Icon(
                    Icons.quiz,
                    size: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    '${question.options.length} خيارات',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _editQuestion(question),
                      icon: Icon(Icons.edit, size: 16.sp),
                      label: Text('تعديل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor.withValues(
                          alpha: 0.1,
                        ),
                        foregroundColor: AppTheme.primaryColor,
                        elevation: 0,
                        padding: EdgeInsets.symmetric(vertical: 8.h),
                      ),
                    ),
                  ),

                  SizedBox(width: 8.w),

                  ElevatedButton.icon(
                    onPressed: () => _duplicateQuestion(question),
                    icon: Icon(Icons.copy, size: 16.sp),
                    label: Text('نسخ'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.secondaryColor.withValues(
                        alpha: 0.1,
                      ),
                      foregroundColor: AppTheme.secondaryColor,
                      elevation: 0,
                      padding: EdgeInsets.symmetric(
                        vertical: 8.h,
                        horizontal: 12.w,
                      ),
                    ),
                  ),

                  SizedBox(width: 8.w),

                  ElevatedButton.icon(
                    onPressed: () => _deleteQuestion(question),
                    icon: Icon(Icons.delete, size: 16.sp),
                    label: Text('حذف'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.errorColor.withValues(
                        alpha: 0.1,
                      ),
                      foregroundColor: AppTheme.errorColor,
                      elevation: 0,
                      padding: EdgeInsets.symmetric(
                        vertical: 8.h,
                        horizontal: 12.w,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getDifficultyColor(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return AppTheme.successColor;
      case DifficultyLevel.medium:
        return AppTheme.warningColor;
      case DifficultyLevel.hard:
        return AppTheme.errorColor;
    }
  }

  void _showAddQuestionDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('صفحة إضافة السؤال - قيد التطوير')),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة الأسئلة'),
        content: const Text(
          'استخدم الفلاتر في الأعلى لتصفية الأسئلة حسب المادة والوحدة والدرس',
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _editQuestion(Question question) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('صفحة تعديل السؤال - قيد التطوير')),
    );
  }

  void _duplicateQuestion(Question question) async {
    try {
      final duplicatedQuestion = question.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        questionText: '${question.questionText} (نسخة)',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await ExamService.instance.createQuestion(duplicatedQuestion);
      _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نسخ السؤال بنجاح'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في نسخ السؤال: $e')));
      }
    }
  }

  void _deleteQuestion(Question question) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا السؤال؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ExamService.instance.deleteQuestion(question.id);
        _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف السؤال بنجاح'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('خطأ في الحذف: $e')));
        }
      }
    }
  }
}
