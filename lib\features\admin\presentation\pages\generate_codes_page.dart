import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:math';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subscription_code_model.dart';

class GenerateCodesPage extends StatefulWidget {
  final VoidCallback? onCodesGenerated;

  const GenerateCodesPage({super.key, this.onCodesGenerated});

  @override
  State<GenerateCodesPage> createState() => _GenerateCodesPageState();
}

class _GenerateCodesPageState extends State<GenerateCodesPage> {
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController(text: '10');
  final _durationController = TextEditingController(text: '30');
  final _prefixController = TextEditingController();

  bool _isLoading = false;
  int _codeLength = 8;
  bool _includeNumbers = true;
  bool _includeLetters = true;
  bool _includeSpecialChars = false;

  @override
  void dispose() {
    _quantityController.dispose();
    _durationController.dispose();
    _prefixController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'إنشاء أكواد التفعيل',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.successGradient),
        ),
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // إعدادات الكمية والمدة
              _buildQuantityAndDurationSection(),

              SizedBox(height: 24.h),

              // إعدادات الكود
              _buildCodeSettingsSection(),

              SizedBox(height: 24.h),

              // معاينة الكود
              _buildPreviewSection(),

              SizedBox(height: 32.h),

              // أزرار الإجراءات
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuantityAndDurationSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الكمية والمدة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            Row(
              children: [
                // عدد الأكواد
                Expanded(
                  child: TextFormField(
                    controller: _quantityController,
                    decoration: const InputDecoration(
                      labelText: 'عدد الأكواد *',
                      border: OutlineInputBorder(),
                      suffixText: 'كود',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final quantity = int.tryParse(value);
                      if (quantity == null || quantity < 1 || quantity > 1000) {
                        return 'من 1 إلى 1000';
                      }
                      return null;
                    },
                  ),
                ),

                SizedBox(width: 16.w),

                // مدة الصلاحية
                Expanded(
                  child: TextFormField(
                    controller: _durationController,
                    decoration: const InputDecoration(
                      labelText: 'مدة الصلاحية *',
                      border: OutlineInputBorder(),
                      suffixText: 'يوم',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final duration = int.tryParse(value);
                      if (duration == null || duration < 1 || duration > 365) {
                        return 'من 1 إلى 365';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCodeSettingsSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الكود',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            // بادئة الكود
            TextFormField(
              controller: _prefixController,
              decoration: const InputDecoration(
                labelText: 'بادئة الكود (اختياري)',
                border: OutlineInputBorder(),
                hintText: 'مثل: ST2024',
              ),
              validator: (value) {
                if (value != null && value.isNotEmpty && value.length > 6) {
                  return 'أقصى 6 أحرف';
                }
                return null;
              },
            ),

            SizedBox(height: 16.h),

            // طول الكود
            Row(
              children: [
                Text(
                  'طول الكود: ',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Expanded(
                  child: Slider(
                    value: _codeLength.toDouble(),
                    min: 6,
                    max: 12,
                    divisions: 6,
                    label: '$_codeLength أحرف',
                    onChanged: (value) {
                      setState(() {
                        _codeLength = value.round();
                      });
                    },
                  ),
                ),
                Text(
                  '$_codeLength',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // خيارات الأحرف
            Text('نوع الأحرف:', style: Theme.of(context).textTheme.titleMedium),

            SizedBox(height: 8.h),

            CheckboxListTile(
              title: const Text('أرقام (0-9)'),
              value: _includeNumbers,
              onChanged: (value) {
                setState(() {
                  _includeNumbers = value!;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),

            CheckboxListTile(
              title: const Text('أحرف (A-Z)'),
              value: _includeLetters,
              onChanged: (value) {
                setState(() {
                  _includeLetters = value!;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),

            CheckboxListTile(
              title: const Text('رموز خاصة (!@#)'),
              value: _includeSpecialChars,
              onChanged: (value) {
                setState(() {
                  _includeSpecialChars = value!;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معاينة الكود',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            SizedBox(height: 16.h),

            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppTheme.backgroundColor,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: AppTheme.textSecondaryColor.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                _generateSampleCode(),
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'monospace',
                  color: AppTheme.primaryColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 8.h),

            Text(
              'مثال على شكل الكود الذي سيتم إنشاؤه',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.textSecondaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Text(
              'إلغاء',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
          ),
        ),

        SizedBox(width: 16.w),

        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _generateCodes,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.successColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'إنشاء الأكواد',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  String _generateSampleCode() {
    if (!_includeNumbers && !_includeLetters && !_includeSpecialChars) {
      return 'يرجى اختيار نوع واحد على الأقل';
    }

    String chars = '';
    if (_includeNumbers) chars += '0123456789';
    if (_includeLetters) chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (_includeSpecialChars) chars += '!@#\$%^&*';

    final random = Random();
    String code = _prefixController.text;

    for (int i = 0; i < _codeLength; i++) {
      code += chars[random.nextInt(chars.length)];
    }

    return code;
  }

  Future<void> _generateCodes() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_includeNumbers && !_includeLetters && !_includeSpecialChars) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار نوع واحد على الأقل من الأحرف'),
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final quantity = int.parse(_quantityController.text);
      final duration = int.parse(_durationController.text);
      final prefix = _prefixController.text;

      String chars = '';
      if (_includeNumbers) chars += '0123456789';
      if (_includeLetters) chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      if (_includeSpecialChars) chars += '!@#\$%^&*';

      final random = Random();
      final batch = FirebaseFirestore.instance.batch();
      final now = DateTime.now();
      final expiryDate = now.add(Duration(days: duration));

      for (int i = 0; i < quantity; i++) {
        String code = prefix;
        for (int j = 0; j < _codeLength; j++) {
          code += chars[random.nextInt(chars.length)];
        }

        final subscriptionCode = SubscriptionCode(
          id: DateTime.now().millisecondsSinceEpoch.toString() + i.toString(),
          code: code,
          subjectIds: [], // سيتم تحديدها لاحقاً
          status: CodeStatus.active,
          createdAt: now,
          expiresAt: expiryDate,
          createdByAdminId: 'admin',
          notes: 'تم إنشاؤه تلقائياً',
        );

        final docRef = FirebaseFirestore.instance
            .collection('subscription_codes')
            .doc(subscriptionCode.id);

        batch.set(docRef, subscriptionCode.toMap());
      }

      await batch.commit();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إنشاء $quantity كود بنجاح'),
            backgroundColor: AppTheme.successColor,
          ),
        );

        widget.onCodesGenerated?.call();
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في إنشاء الأكواد: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
