import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import 'subjects_management_page.dart';
import 'questions_management_page.dart';
import 'codes_management_page.dart';
import 'exams_management_page.dart';
import 'statistics_page.dart';
import 'data_management_page.dart';

class AdminHomePage extends StatelessWidget {
  const AdminHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Smart Test - الأدمن',
          style: Theme.of(
            context,
          ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.secondaryGradient),
        ),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'لوحة التحكم',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            SizedBox(height: 24.h),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16.w,
                mainAxisSpacing: 16.h,
                childAspectRatio: 1.2,
                children: [
                  _buildAdminCard(
                    context,
                    'إدارة المواد',
                    Icons.book,
                    AppTheme.primaryColor,
                    () => _navigateToPage(context, 'subjects'),
                  ),
                  _buildAdminCard(
                    context,
                    'إدارة الأسئلة',
                    Icons.quiz,
                    AppTheme.secondaryColor,
                    () => _navigateToPage(context, 'questions'),
                  ),
                  _buildAdminCard(
                    context,
                    'إدارة الأكواد',
                    Icons.vpn_key,
                    AppTheme.accentColor,
                    () => _navigateToPage(context, 'codes'),
                  ),
                  _buildAdminCard(
                    context,
                    'إدارة الاختبارات',
                    Icons.assignment,
                    AppTheme.warningColor,
                    () => _navigateToPage(context, 'exams'),
                  ),
                  _buildAdminCard(
                    context,
                    'الإحصائيات',
                    Icons.analytics,
                    AppTheme.successColor,
                    () => _navigateToPage(context, 'statistics'),
                  ),
                  _buildAdminCard(
                    context,
                    'إدارة البيانات',
                    Icons.storage,
                    AppTheme.errorColor,
                    () => _navigateToPage(context, 'data'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdminCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      shadowColor: color.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16.r),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 50.w,
                  height: 50.h,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(25.r),
                  ),
                  child: Icon(icon, color: Colors.white, size: 24.sp),
                ),
                SizedBox(height: 12.h),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToPage(BuildContext context, String page) {
    Widget? targetPage;
    String message = '';

    switch (page) {
      case 'subjects':
        targetPage = const SubjectsManagementPage();
        break;
      case 'questions':
        targetPage = const QuestionsManagementPage();
        break;
      case 'codes':
        targetPage = const CodesManagementPage();
        break;
      case 'exams':
        targetPage = const ExamsManagementPage();
        break;
      case 'statistics':
        targetPage = const StatisticsPage();
        break;
      case 'data':
        targetPage = const DataManagementPage();
        break;
      default:
        message = 'هذه الميزة قيد التطوير';
    }

    if (targetPage != null) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => targetPage!),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppTheme.primaryColor,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
