import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import 'units_page.dart';
import 'questions_page.dart';
import 'exams_page.dart';

class SubjectDetailPage extends StatefulWidget {
  final Subject subject;

  const SubjectDetailPage({
    super.key,
    required this.subject,
  });

  @override
  State<SubjectDetailPage> createState() => _SubjectDetailPageState();
}

class _SubjectDetailPageState extends State<SubjectDetailPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 200.h,
              floating: false,
              pinned: true,
              backgroundColor: Colors.transparent,
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        _getSubjectColor(),
                        _getSubjectColor().withValues(alpha: 0.8),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: EdgeInsets.all(20.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 60.w,
                                height: 60.h,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(16.r),
                                ),
                                child: Icon(
                                  _getSubjectIcon(),
                                  color: Colors.white,
                                  size: 30.sp,
                                ),
                              ),
                              SizedBox(width: 16.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      widget.subject.name,
                                      style: TextStyle(
                                        fontSize: 24.sp,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                    SizedBox(height: 4.h),
                                    Text(
                                      widget.subject.description,
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        color: Colors.white.withValues(alpha: 0.9),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              bottom: TabBar(
                controller: _tabController,
                indicatorColor: Colors.white,
                indicatorWeight: 3,
                labelColor: Colors.white,
                unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
                labelStyle: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.normal,
                ),
                tabs: const [
                  Tab(
                    icon: Icon(Icons.book_outlined),
                    text: 'الوحدات',
                  ),
                  Tab(
                    icon: Icon(Icons.quiz_outlined),
                    text: 'الأسئلة',
                  ),
                  Tab(
                    icon: Icon(Icons.school_outlined),
                    text: 'الدورات',
                  ),
                  Tab(
                    icon: Icon(Icons.assignment_outlined),
                    text: 'الاختبارات',
                  ),
                ],
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            // تبويب الوحدات والدروس
            UnitsPage(subject: widget.subject),
            
            // تبويب الأسئلة العادية
            QuestionsPage(
              subject: widget.subject,
              isCourseQuestions: false,
            ),
            
            // تبويب أسئلة الدورات
            QuestionsPage(
              subject: widget.subject,
              isCourseQuestions: true,
            ),
            
            // تبويب الاختبارات
            SubjectExamsPage(subject: widget.subject),
          ],
        ),
      ),
    );
  }

  Color _getSubjectColor() {
    final name = widget.subject.name.toLowerCase();
    if (name.contains('رياضيات') || name.contains('math')) {
      return AppTheme.primaryColor;
    } else if (name.contains('فيزياء') || name.contains('physics')) {
      return AppTheme.secondaryColor;
    } else if (name.contains('كيمياء') || name.contains('chemistry')) {
      return AppTheme.accentColor;
    } else if (name.contains('أحياء') || name.contains('biology')) {
      return AppTheme.successColor;
    } else if (name.contains('عربية') || name.contains('arabic')) {
      return AppTheme.warningColor;
    } else if (name.contains('إنجليزية') || name.contains('english')) {
      return AppTheme.errorColor;
    }
    return AppTheme.primaryColor;
  }

  IconData _getSubjectIcon() {
    final name = widget.subject.name.toLowerCase();
    if (name.contains('رياضيات') || name.contains('math')) {
      return Icons.calculate;
    } else if (name.contains('فيزياء') || name.contains('physics')) {
      return Icons.science;
    } else if (name.contains('كيمياء') || name.contains('chemistry')) {
      return Icons.biotech;
    } else if (name.contains('أحياء') || name.contains('biology')) {
      return Icons.eco;
    } else if (name.contains('عربية') || name.contains('arabic')) {
      return Icons.language;
    } else if (name.contains('إنجليزية') || name.contains('english')) {
      return Icons.translate;
    }
    return Icons.book;
  }
}

// صفحة الاختبارات الخاصة بالمادة
class SubjectExamsPage extends StatelessWidget {
  final Subject subject;

  const SubjectExamsPage({
    super.key,
    required this.subject,
  });

  @override
  Widget build(BuildContext context) {
    return ExamsPage(subjectId: subject.id);
  }
}
